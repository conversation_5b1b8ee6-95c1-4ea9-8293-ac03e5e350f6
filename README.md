# TwTwins - Sistema de Gestão de Fluxo de Caixa

TwTwins é um sistema de gestão financeira desenvolvido para controlar o fluxo de caixa, gerenciar usuários e gerar relatórios financeiros. A aplicação oferece uma interface moderna e responsiva, construída com as melhores práticas de desenvolvimento web.

![TwTwins Logo](/public/logo.png)

## Funcionalidades Principais

### Dashboard
- Visão geral das finanças com gráficos e estatísticas
- Resumo de entradas, saídas e saldo atual
- Visualização de tendências financeiras

### Fluxo de Caixa
- Registro de transações (entradas e saídas)
- Categorização de transações
- Filtros por data, tipo e categoria
- Upload de comprovativos
- Visualização detalhada do histórico financeiro

### Usuários
- Gerenciamento de usuários do sistema
- Controle de permissões (administrador e gerente)
- Edição de perfis de usuário

### Relatórios
- Relatório de Fluxo de Caixa Mensal
- Relatório de Transações por Categoria
- Projeções Financeiras
- Exportação de dados

### Configurações
- Personalização do sistema
- Configurações de conta

## Tecnologias Utilizadas

- **Frontend**:
  - React
  - TypeScript
  - Vite (build tool)
  - Tailwind CSS
  - shadcn/ui (componentes)
  - React Router (navegação)
  - React Query (gerenciamento de estado e requisições)
  - Recharts (gráficos)
  - Lucide React (ícones)

- **Backend**:
  - Supabase (Backend as a Service)
  - PostgreSQL (banco de dados)
  - Autenticação e autorização

## Requisitos do Sistema

- Node.js (versão recomendada: 18.x ou superior)
- npm (gerenciador de pacotes)
- Navegador moderno (Chrome, Firefox, Safari, Edge)

## Instalação e Configuração

### Pré-requisitos
- Node.js & npm instalados - [instalar com nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- Conta no Supabase (para o backend)

### Passos para Instalação

1. Clone o repositório:
   ```sh
   git clone <URL_DO_REPOSITÓRIO>
   ```

2. Navegue até o diretório do projeto:
   ```sh
   cd twtwins
   ```

3. Instale as dependências:
   ```sh
   npm install
   ```

4. Configure as variáveis de ambiente:
   - Crie um arquivo `.env` na raiz do projeto
   - Adicione as variáveis necessárias (consulte `.env.example` se disponível)

5. Inicie o servidor de desenvolvimento:
   ```sh
   npm run dev
   ```

6. Acesse a aplicação em seu navegador:
   ```
   http://localhost:8080
   ```

## Estrutura do Projeto

```
twtwins/
├── public/              # Arquivos estáticos
├── src/                 # Código fonte
│   ├── components/      # Componentes React reutilizáveis
│   ├── hooks/           # Custom hooks
│   ├── integrations/    # Integrações com serviços externos (Supabase)
│   ├── lib/             # Utilitários e funções auxiliares
│   ├── pages/           # Componentes de página
│   ├── App.tsx          # Componente principal da aplicação
│   └── main.tsx         # Ponto de entrada da aplicação
├── .env                 # Variáveis de ambiente (não versionado)
├── index.html           # Template HTML
├── package.json         # Dependências e scripts
├── tailwind.config.ts   # Configuração do Tailwind CSS
└── vite.config.ts       # Configuração do Vite
```

## Autenticação e Autorização

O sistema utiliza o Supabase para autenticação de usuários e possui dois níveis de acesso:

- **Administrador**: Acesso completo ao sistema, incluindo gerenciamento de usuários
- **Gerente**: Acesso limitado, sem permissão para visualizar certos dados

## Desenvolvimento

### Scripts Disponíveis

- `npm run dev` - Inicia o servidor de desenvolvimento
- `npm run build` - Compila o projeto para produção
- `npm run build:dev` - Compila o projeto para ambiente de desenvolvimento
- `npm run preview` - Visualiza a versão compilada localmente

### Padrões de Código

- Utilize TypeScript para tipagem estática
- Siga os padrões de componentes estabelecidos
- Mantenha a estrutura de arquivos organizada
- Documente alterações significativas

## Suporte

Para suporte ou dúvidas, entre em contato com o desenvolvedor:

- **Desenvolvedor**: Carlos Cesar
- **Email**: [<EMAIL>]

## Licença

Este projeto é proprietário e seu uso é restrito aos termos estabelecidos pelo proprietário.

---

© 2024 TwTwins. Todos os direitos reservados.
