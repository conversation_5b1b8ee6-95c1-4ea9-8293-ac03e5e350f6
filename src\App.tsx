
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HashRouter, Routes, Route, Navigate } from "react-router-dom";
import { useState, useEffect } from "react";
import AppLayout from "./components/AppLayout";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import FluxoDeCaixa from "./pages/FluxoDeCaixa";
import Usuarios from "./pages/Usuarios";
import Relatorios from "./pages/Relatorios";
import Configuracoes from "./pages/Configuracoes";
import NotFound from "./pages/NotFound";
import { supabase } from "./integrations/supabase/client";
import { FullScreenLoading } from "./components/ui/loading";
import { RoleGuard } from "./components/RoleGuard";

const queryClient = new QueryClient();

const App = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    // Verificar a sessão atual
    const checkSession = async () => {
      const { data } = await supabase.auth.getSession();
      setIsAuthenticated(!!data.session);
    };

    checkSession();

    // Escutar mudanças na autenticação
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      setIsAuthenticated(!!session);
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  // Componente de guarda para rotas protegidas
  const RequireAuth = ({ children }: { children: JSX.Element }) => {
    if (isAuthenticated === null) {
      // Estado inicial, ainda carregando
      return <FullScreenLoading text="Verificando autenticação..." />;
    }

    if (!isAuthenticated) {
      // Não autenticado, redirecionar para login
      return <Navigate to="/login" replace />;
    }

    // Autenticado, mostrar conteúdo
    return children;
  };

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <HashRouter>
          <Routes>
            {/* Rota pública de login */}
            <Route
              path="/login"
              element={isAuthenticated ? <Navigate to="/" replace /> : <Login />}
            />

            {/* Rotas protegidas */}
            <Route element={<RequireAuth><AppLayout /></RequireAuth>}>
              <Route path="/" element={<Dashboard />} />
              <Route path="/fluxo-de-caixa" element={<FluxoDeCaixa />} />
              <Route path="/usuarios" element={
                <RoleGuard allowedRoles={['administrador', 'gerente']}>
                  <Usuarios />
                </RoleGuard>
              } />
              <Route path="/relatorios" element={<Relatorios />} />
              <Route path="/configuracoes" element={<Configuracoes />} />
            </Route>

            {/* Rota 404 */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </HashRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
