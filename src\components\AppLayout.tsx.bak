
import React, { useState, useEffect, createContext } from "react";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  ChevronLeft,
  LogOut,
  LayoutDashboard,
  CreditCard,
  Users,
  BarChart4,
  Settings,
  Menu,
  X
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { UserAvatar } from "./users/UserAvatar";
import { toast } from "@/components/ui/sonner";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

// Contexto para compartilhar o estado do sidebar
interface SidebarContextType {
  collapsed: boolean;
  isMobile: boolean;
  setMobileOpen?: (open: boolean) => void;
}

const SidebarContext = createContext<SidebarContextType>({ collapsed: false, isMobile: false });

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
}

const NavItem = ({ to, icon, label, isActive }: NavItemProps) => {
  // Acesso ao estado collapsed e mobile do componente pai
  const { collapsed, isMobile, setMobileOpen } = React.useContext(SidebarContext);
  const navigate = useNavigate();

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (isMobile && setMobileOpen) {
      e.preventDefault();
      // Fecha o menu mobile e navega para a rota
      setMobileOpen(false);
      navigate(to);
    }
  };

  return (
    <Link
      to={to}
      onClick={handleClick}
      className={cn(
        "flex items-center rounded-md text-sm font-medium transition-all",
        isActive
          ? "bg-white/20 text-white"
          : "text-white/80 hover:text-white hover:bg-white/10",
        collapsed && !isMobile
          ? "justify-center px-2 py-2" // Centraliza o ícone quando recolhido (apenas desktop)
          : "gap-3 px-3 py-2" // Mantém o espaçamento normal quando expandido ou mobile
      )}
      title={collapsed && !isMobile ? label : undefined} // Adiciona tooltip quando recolhido (apenas desktop)
    >
      <div className="w-5 h-5 flex items-center justify-center">{icon}</div>
      {(!collapsed || isMobile) && <span>{label}</span>}
    </Link>
  );
};

const AppLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [userName, setUserName] = useState<string>('');
  const [userInitials, setUserInitials] = useState<string>('');
  const [mobileOpen, setMobileOpen] = useState(false);
  const isMobile = useIsMobile();
  const location = useLocation();
  const navigate = useNavigate();
  const pathname = location.pathname;

  useEffect(() => {
    // Verificar se o usuário está autenticado
    const checkAuth = async () => {
      const { data } = await supabase.auth.getSession();

      if (!data.session) {
        navigate('/login');
        return;
      }

      // Carregar dados do perfil do usuário
      const { data: profileData, error } = await supabase
        .from('profiles')
        .select('name, role')
        .eq('id', data.session.user.id)
        .single();

      if (profileData) {
        setUserName(profileData.name);
        setUserInitials(profileData.name.substring(0, 2).toUpperCase());
      }
    };

    checkAuth();

    // Escutar mudanças na autenticação
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT') {
        navigate('/login');
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [navigate]);

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      toast.success('Logout realizado com sucesso!');
      navigate('/login');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
      toast.error('Erro ao fazer logout');
    }
  };

  // Fechar o menu mobile quando a rota muda
  useEffect(() => {
    if (isMobile) {
      setMobileOpen(false);
    }
  }, [pathname, isMobile]);

  // Renderização do menu de navegação (usado tanto no desktop quanto no mobile)
  const renderNavigation = () => (
    <nav className="flex-1 p-2 space-y-1">
      <NavItem
        to="/"
        icon={<LayoutDashboard size={18} />}
        label="Dashboard"
        isActive={pathname === "/"}
      />
      <NavItem
        to="/fluxo-de-caixa"
        icon={<CreditCard size={18} />}
        label="Fluxo de Caixa"
        isActive={pathname === "/fluxo-de-caixa"}
      />
      <NavItem
        to="/usuarios"
        icon={<Users size={18} />}
        label="Usuários"
        isActive={pathname === "/usuarios"}
      />
      <NavItem
        to="/relatorios"
        icon={<BarChart4 size={18} />}
        label="Relatórios"
        isActive={pathname === "/relatorios"}
      />
      <NavItem
        to="/configuracoes"
        icon={<Settings size={18} />}
        label="Configurações"
        isActive={pathname === "/configuracoes"}
      />
    </nav>
  );

  // Renderização do perfil do usuário (usado tanto no desktop quanto no mobile)
  const renderUserProfile = () => (
    <div className={cn(
      "border-t border-white/10 flex transition-all",
      collapsed && !isMobile ? "flex-col p-2 gap-2 items-center" : "flex-row p-4 gap-3 items-center"
    )}>
      {/* Avatar sempre visível */}
      <UserAvatar name={userName || '--'} className="w-8 h-8 text-sm" title={collapsed && !isMobile ? userName : undefined} />

      {/* Informações do usuário - visíveis quando expandido ou no mobile */}
      {(!collapsed || isMobile) && (
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">{userName || 'Carregando...'}</p>
          <p className="text-xs text-white/70 truncate">Sistema de Gestão</p>
        </div>
      )}

      {/* Botão de logout */}
      <button
        onClick={handleLogout}
        className="p-1 rounded-md hover:bg-white/10 transition-colors"
        title="Sair"
      >
        <LogOut size={16} />
      </button>
    </div>
  );

  return (
    <SidebarContext.Provider value={{ collapsed, isMobile, setMobileOpen: setMobileOpen }}>
      <div className="flex min-h-screen bg-gray-50">
        {/* Header Mobile com botão hambúrguer */}
        {isMobile && (
          <header className="fixed top-0 left-0 right-0 h-14 bg-twtwins-purple text-white z-20 flex items-center justify-between px-4">
            <h1 className="text-xl font-bold">TwTwins</h1>
            <Sheet open={mobileOpen} onOpenChange={setMobileOpen}>
              <SheetTrigger asChild>
                <button className="p-2 rounded-md hover:bg-white/10">
                  <Menu size={24} />
                </button>
              </SheetTrigger>
              <SheetContent side="left" className="p-0 bg-twtwins-purple text-white w-[280px] border-r-0" hideCloseButton={true}>
                <div className="flex flex-col h-full">
                  <div className="p-4 border-b border-white/10 flex items-center justify-between">
                    <h2 className="text-xl font-bold">Menu</h2>
                    <button
                      onClick={() => setMobileOpen(false)}
                      className="p-1 rounded-md hover:bg-white/10"
                      aria-label="Fechar menu"
                    >
                      <X size={20} />
                    </button>
                  </div>
                  {renderNavigation()}
                  {renderUserProfile()}
                </div>
              </SheetContent>
            </Sheet>
          </header>
        )}

        {/* Sidebar - Fixed (apenas desktop) */}
        {!isMobile && (
          <aside
            className={cn(
              "bg-twtwins-purple text-white flex flex-col transition-all duration-300 fixed h-screen z-10",
              collapsed ? "w-[70px]" : "w-[240px]"
            )}
          >
            {/* Logo */}
            <div className="p-4 flex items-center justify-between border-b border-white/10">
              {!collapsed && <h1 className="text-xl font-bold">TwTwins</h1>}
              <button
                onClick={() => setCollapsed(!collapsed)}
                className="p-1 rounded-md hover:bg-white/10 transition-colors"
                aria-label={collapsed ? "Expandir menu" : "Recolher menu"}
              >
                <ChevronLeft
                  className={cn(
                    "h-5 w-5 transition-transform",
                    collapsed && "rotate-180"
                  )}
                />
              </button>
            </div>

            {/* Navigation - Desktop */}
            {renderNavigation()}

            {/* User Profile - Desktop */}
            {renderUserProfile()}
          </aside>
        )}

        {/* Main Content */}
        <main
          className={cn(
            "flex-1 overflow-y-auto min-h-screen",
            isMobile
              ? "pt-20 px-4 pb-4" // Espaço aumentado para o header mobile (pt-14 -> pt-20)
              : "p-6", // Padding normal para desktop
            !isMobile && (collapsed ? "ml-[70px]" : "ml-[240px]") // Margem apenas no desktop
          )}
        >
          <Outlet />
        </main>
      </div>
    </SidebarContext.Provider>
  );
};

export default AppLayout;
