
import StatCard from "@/components/StatCard";
import { TrendingUp, CreditCard, Wallet, Users } from "lucide-react";
import { formatCurrency } from "@/lib/formatters";

interface DashboardStatsProps {
  totalEntradas: number;
  totalSaidas: number;
  saldoAtual: number;
  activeUsersCount: number;
  percentChanges: {
    entradas: number;
    saidas: number;
    saldo: number;
    users: number;
  };
  showUsersCard?: boolean;
}

export const DashboardStats = ({
  totalEntradas,
  totalSaidas,
  saldoAtual,
  activeUsersCount,
  percentChanges,
  showUsersCard = true
}: DashboardStatsProps) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 ${showUsersCard ? 'lg:grid-cols-4' : 'lg:grid-cols-3'} gap-4 mb-8`}>
      <StatCard
        title="Total Entradas"
        value={formatCurrency(totalEntradas)}
        variant="green"
        icon={<TrendingUp className="h-5 w-5 text-twtwins-green" />}
        percentChange={{ 
          value: percentChanges.entradas, 
          label: "desde o mês passado" 
        }}
      />
      <StatCard
        title="Total Saídas"
        value={formatCurrency(totalSaidas)}
        variant="red"
        icon={<CreditCard className="h-5 w-5 text-twtwins-red" />}
        percentChange={{ 
          value: percentChanges.saidas, 
          label: "desde o mês passado" 
        }}
      />
      <StatCard
        title="Saldo Atual"
        value={formatCurrency(saldoAtual)}
        variant="blue"
        icon={<Wallet className="h-5 w-5 text-twtwins-blue" />}
        percentChange={{ 
          value: percentChanges.saldo, 
          label: "desde o mês passado" 
        }}
      />
      {showUsersCard && (
        <StatCard
          title="Usuários Ativos"
          value={activeUsersCount.toString()}
          variant="purple"
          icon={<Users className="h-5 w-5 text-twtwins-purple" />}
          percentChange={{
            value: percentChanges.users,
            label: "desde o mês passado"
          }}
        />
      )}
    </div>
  );
}
