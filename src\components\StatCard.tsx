
import { cn, capPercentage } from "@/lib/utils";
import { ReactNode } from "react";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface StatCardProps {
  title: string;
  value: string | ReactNode;
  variant?: "green" | "red" | "blue" | "purple" | "default";
  icon?: ReactNode;
  percentChange?: {
    value: number;
    label: string;
  };
  className?: string;
}

const StatCard = ({
  title,
  value,
  variant = "default",
  icon,
  percentChange,
  className,
}: StatCardProps) => {
  const cardClasses = {
    green: "stat-card-green",
    red: "stat-card-red",
    blue: "stat-card-blue",
    purple: "stat-card-purple",
    default: "",
  };

  // Format the percent change display
  const formatPercentChange = (value: number) => {
    if (isNaN(value)) return "N/A";
    
    // Exact 0% shouldn't have a sign
    if (value === 0) return "0%";
    
    // Cap the percentage value and check if it was capped
    const { value: cappedValue, capped } = capPercentage(value);
    
    // Determine sign prefix for non-zero values
    const prefix = cappedValue > 0 ? "+" : "";
    
    // Add the '+' suffix for capped values
    const suffix = capped ? "+" : "";
    
    return `${prefix}${cappedValue}%${suffix}`;
  };

  // Get the original uncapped percent value for the tooltip
  const getOriginalPercentValue = (value: number) => {
    if (isNaN(value) || value === 0) return null;
    
    // If the value is capped, we want to show the original value in the tooltip
    if (Math.abs(value) > 100) {
      const prefix = value > 0 ? "+" : "";
      return `${prefix}${value.toFixed(1)}%`;
    }
    
    return null;
  };

  const originalPercentValue = percentChange ? getOriginalPercentValue(percentChange.value) : null;

  return (
    <div className={cn("stat-card", cardClasses[variant], className)}>
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
          <div className="text-[1.2rem] font-bold mt-1">{value}</div>

          {percentChange && (
            <div className="mt-2 text-sm">
              {originalPercentValue ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span
                        className={
                          percentChange.value > 0 
                            ? "percent-positive" 
                            : percentChange.value < 0 
                              ? "percent-negative" 
                              : "text-muted-foreground"
                        }
                      >
                        {formatPercentChange(percentChange.value)}
                        <span className="text-muted-foreground text-xs ml-1">
                          {percentChange.label}
                        </span>
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Valor real: {originalPercentValue}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <span
                  className={
                    percentChange.value > 0 
                      ? "percent-positive" 
                      : percentChange.value < 0 
                        ? "percent-negative" 
                        : "text-muted-foreground"
                  }
                >
                  {formatPercentChange(percentChange.value)}
                  <span className="text-muted-foreground text-xs ml-1">
                    {percentChange.label}
                  </span>
                </span>
              )}
            </div>
          )}
        </div>

        {icon && (
          <div className="bg-muted rounded-full p-2 opacity-80">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
};

export default StatCard;
