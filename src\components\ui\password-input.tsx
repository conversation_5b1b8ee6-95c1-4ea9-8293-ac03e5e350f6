import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff } from "lucide-react";
import { cn } from "@/lib/utils";
import { calculatePasswordStrength, PasswordStrength } from "@/lib/passwordUtils";

interface PasswordInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  showStrengthIndicator?: boolean;
  strengthIndicatorClassName?: string;
}

const getStrengthColor = (strength: PasswordStrength) => {
  switch (strength) {
    case 'weak':
      return 'text-red-500 bg-red-100';
    case 'medium':
      return 'text-yellow-600 bg-yellow-100';
    case 'strong':
      return 'text-green-600 bg-green-100';
    default:
      return 'text-gray-500 bg-gray-100';
  }
};

const getStrengthBarColor = (strength: PasswordStrength) => {
  switch (strength) {
    case 'weak':
      return 'bg-red-500';
    case 'medium':
      return 'bg-yellow-500';
    case 'strong':
      return 'bg-green-500';
    default:
      return 'bg-gray-300';
  }
};

const getStrengthText = (strength: PasswordStrength) => {
  switch (strength) {
    case 'weak':
      return 'Fraca';
    case 'medium':
      return 'Média';
    case 'strong':
      return 'Forte';
    default:
      return '';
  }
};

export const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, showStrengthIndicator = false, strengthIndicatorClassName, value, onChange, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const passwordValue = value as string || '';
    const strengthResult = showStrengthIndicator ? calculatePasswordStrength(passwordValue) : null;

    return (
      <div className="space-y-2">
        <div className="relative">
          <Input
            type={showPassword ? "text" : "password"}
            className={cn("pr-10", className)}
            ref={ref}
            value={value}
            onChange={onChange}
            {...props}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            onClick={() => setShowPassword(!showPassword)}
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-muted-foreground" />
            ) : (
              <Eye className="h-4 w-4 text-muted-foreground" />
            )}
            <span className="sr-only">
              {showPassword ? "Ocultar senha" : "Mostrar senha"}
            </span>
          </Button>
        </div>

        {showStrengthIndicator && passwordValue && strengthResult && (
          <div className={cn("space-y-2", strengthIndicatorClassName)}>
            {/* Strength bar */}
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5, 6].map((level) => (
                <div
                  key={level}
                  className={cn(
                    "h-2 flex-1 rounded-full transition-colors",
                    level <= strengthResult.score
                      ? getStrengthBarColor(strengthResult.strength)
                      : "bg-gray-200"
                  )}
                />
              ))}
            </div>

            {/* Strength text and feedback */}
            <div className="flex items-center justify-between">
              <span
                className={cn(
                  "text-xs font-medium px-2 py-1 rounded-full",
                  getStrengthColor(strengthResult.strength)
                )}
              >
                {getStrengthText(strengthResult.strength)}
              </span>
            </div>

            {/* Feedback */}
            {strengthResult.feedback.length > 0 && strengthResult.strength !== 'strong' && (
              <ul className="text-xs text-muted-foreground space-y-1">
                {strengthResult.feedback.slice(0, 2).map((feedback, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-1 h-1 bg-muted-foreground rounded-full mr-2" />
                    {feedback}
                  </li>
                ))}
              </ul>
            )}
          </div>
        )}
      </div>
    );
  }
);

PasswordInput.displayName = "PasswordInput";
