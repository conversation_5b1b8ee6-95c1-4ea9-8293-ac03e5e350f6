
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { getAuthErrorMessage } from "@/utils/authErrorMessages";
import { useCurrentUser } from "@/hooks/useCurrentUser";

export interface User {
  id: string;
  name: string;
  email: string;
  role: "administrador" | "gerente" | "estagiario";
  status: boolean;
  phone: string | null;
  last_login: string | null;
  avatar: string;
}

interface EditUserFormProps {
  user: Partial<User>;
  onClose: () => void;
}

export const EditUserForm = ({ user, onClose }: EditUserFormProps) => {
  const [editUserForm, setEditUserForm] = useState<Partial<User>>(user);
  const [passwordForm, setPasswordForm] = useState({
    newPassword: '',
    confirmPassword: ''
  });
  const [showPasswordFields, setShowPasswordFields] = useState(false);
  const queryClient = useQueryClient();
  const { currentUser, isAdmin } = useCurrentUser();

  // Update local form state when user prop changes
  useEffect(() => {
    setEditUserForm(user);
  }, [user]);
  
  const updateUserMutation = useMutation({
    mutationFn: async (userData: { user: Partial<User>, password?: { newPassword: string, confirmPassword: string } }) => {
      // Update profile data
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          name: userData.user.name,
          phone: userData.user.phone,
          role: userData.user.role,
          status: userData.user.status,
        })
        .eq('id', userData.user.id);

      if (profileError) throw profileError;

      // Update password if provided (admin only)
      if (userData.password && userData.password.newPassword && isAdmin && userData.user.id !== currentUser?.id) {
        if (userData.password.newPassword !== userData.password.confirmPassword) {
          throw new Error("As senhas não coincidem!");
        }

        // Call Edge Function to update user password
        const { data: functionData, error: functionError } = await supabase.functions.invoke('update-user-password', {
          body: {
            userId: userData.user.id,
            newPassword: userData.password.newPassword
          }
        });

        if (functionError) {
          console.error("Error updating password:", functionError);
          throw new Error("Erro ao atualizar senha: " + functionError.message);
        }

        if (functionData?.error) {
          throw new Error("Erro ao atualizar senha: " + functionData.error);
        }
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      onClose();

      if (variables.password && variables.password.newPassword) {
        toast.success("Usuário e senha atualizados com sucesso!");
      } else {
        toast.success("Usuário atualizado com sucesso!");
      }
    },
    onError: (error) => {
      console.error(error);
      const friendlyMessage = getAuthErrorMessage(error);
      toast.error(`Erro ao atualizar usuário: ${friendlyMessage}`);
    }
  });
  
  const handleEditUserSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate password fields if they are being used
    if (showPasswordFields && passwordForm.newPassword) {
      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        toast.error("As senhas não coincidem!");
        return;
      }
      if (passwordForm.newPassword.length < 6) {
        toast.error("A senha deve ter pelo menos 6 caracteres!");
        return;
      }
    }

    updateUserMutation.mutate({
      user: editUserForm,
      password: showPasswordFields && passwordForm.newPassword ? passwordForm : undefined
    });
  };

  return (
    <form onSubmit={handleEditUserSubmit}>
      <div className="grid gap-4 py-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Nome <span className="text-red-500">*</span></label>
          <Input
            placeholder="Nome completo do usuário"
            value={editUserForm.name || ""}
            onChange={(e) => setEditUserForm({...editUserForm, name: e.target.value})}
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Email <span className="text-red-500">*</span></label>
          <Input
            type="email"
            placeholder="<EMAIL>"
            value={editUserForm.email || ""}
            onChange={(e) => setEditUserForm({...editUserForm, email: e.target.value})}
            required
            disabled
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Telefone</label>
            <Input
              placeholder="Número de telefone"
              value={editUserForm.phone || ""}
              onChange={(e) => setEditUserForm({...editUserForm, phone: e.target.value})}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Perfil <span className="text-red-500">*</span></label>
            <Select
              value={editUserForm.role || "gerente"}
              onValueChange={(value: "administrador" | "gerente" | "estagiario") => setEditUserForm({...editUserForm, role: value})}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="administrador">Administrador</SelectItem>
                <SelectItem value="gerente">Gerente</SelectItem>
                <SelectItem value="estagiario">Estagiário</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Status</label>
          <Select 
            value={editUserForm.status ? "true" : "false"}
            onValueChange={(value) => setEditUserForm({...editUserForm, status: value === "true"})}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Ativo</SelectItem>
              <SelectItem value="false">Inativo</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Password Reset Section - Only for Admins editing other users */}
        {isAdmin && user.id !== currentUser?.id && (
          <div className="space-y-4 border-t pt-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Redefinir Senha</label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  setShowPasswordFields(!showPasswordFields);
                  if (!showPasswordFields) {
                    setPasswordForm({ newPassword: '', confirmPassword: '' });
                  }
                }}
              >
                {showPasswordFields ? 'Cancelar' : 'Alterar Senha'}
              </Button>
            </div>

            {showPasswordFields && (
              <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Nova Senha <span className="text-red-500">*</span></label>
                  <PasswordInput
                    placeholder="Digite a nova senha"
                    value={passwordForm.newPassword}
                    onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}
                    required={showPasswordFields}
                    showStrengthIndicator={true}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Confirmar Nova Senha <span className="text-red-500">*</span></label>
                  <PasswordInput
                    placeholder="Confirme a nova senha"
                    value={passwordForm.confirmPassword}
                    onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
                    required={showPasswordFields}
                  />
                  {passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword && (
                    <p className="text-sm text-red-500">As senhas não coincidem</p>
                  )}
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-sm text-blue-800">
                    <strong>Atenção:</strong> A nova senha será aplicada imediatamente.
                    O usuário deverá usar a nova senha no próximo login.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <DialogFooter>
        <Button 
          variant="outline" 
          onClick={onClose} 
          type="button"
        >
          Cancelar
        </Button>
        <Button 
          className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
          type="submit"
          disabled={updateUserMutation.isPending}
        >
          {updateUserMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Atualizando...
            </>
          ) : (
            'Salvar'
          )}
        </Button>
      </DialogFooter>
    </form>
  );
};
