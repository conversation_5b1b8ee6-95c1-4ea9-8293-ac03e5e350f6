
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import { Loader2, FileText, Download } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { getAuthErrorMessage } from "@/utils/authErrorMessages";
import { generateCredentialsPDF } from "@/utils/pdfGenerator";

export interface NewUserFormData {
  name: string;
  email: string;
  phone: string;
  role: "administrador" | "gerente" | "estagiario";
  password: string;
  confirmPassword: string;
}

const initialNewUserForm: NewUserFormData = {
  name: "",
  email: "",
  phone: "",
  role: "gerente",
  password: "",
  confirmPassword: "",
};

interface NewUserFormProps {
  onClose: () => void;
}

export const NewUserForm = ({ onClose }: NewUserFormProps) => {
  const [newUserForm, setNewUserForm] = useState<NewUserFormData>(initialNewUserForm);

  const [userCreated, setUserCreated] = useState<{
    name: string;
    email: string;
    password: string;
    role: string;
    createdAt: string;
  } | null>(null);

  const queryClient = useQueryClient();
  
  const createUserMutation = useMutation({
    mutationFn: async (userData: NewUserFormData) => {
      // Capture current session before creating new user (in case we need to restore it)
      const { data: currentSession } = await supabase.auth.getSession();
      
      try {
        // Call the Edge Function that uses the service role key to create the user
        const { data, error } = await supabase.functions.invoke('create-user', {
          body: {
            email: userData.email,
            password: userData.password,
            name: userData.name,
            phone: userData.phone,
            role: userData.role,
          }
        });
        
        if (error) throw error;
        if (data.error) throw new Error(data.error);
        
        // Check if session was affected
        const { data: updatedSession } = await supabase.auth.getSession();
        
        // If the session changed unexpectedly, log the issue but don't interrupt
        if (currentSession.session?.user?.id !== updatedSession.session?.user?.id) {
          console.warn("Session changed during user creation - attempting to restore");
          
          // No need to attempt a restore here, as the auth state might be handled by onAuthStateChange
          // in App.tsx - just note the issue and return data to allow the process to complete
        }
        
        return data;
      } catch (error) {
        console.error("Error in user creation:", error);
        // Still allow PDF generation even if Edge Function fails
        setUserCreated({
          name: userData.name,
          email: userData.email,
          password: userData.password,
          role: userData.role,
          createdAt: new Date().toLocaleDateString('pt-BR')
        });
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });

      // Store user data for PDF generation
      setUserCreated({
        name: newUserForm.name,
        email: newUserForm.email,
        password: newUserForm.password,
        role: newUserForm.role,
        createdAt: new Date().toLocaleDateString('pt-BR')
      });

      toast.success("Usuário criado com sucesso!");
    },
    onError: (error) => {
      console.error(error);
      const friendlyMessage = getAuthErrorMessage(error);
      toast.error(`Erro ao criar usuário: ${friendlyMessage}. Você ainda pode gerar o PDF de credenciais.`);
    }
  });
  
  const handleNewUserSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (newUserForm.password !== newUserForm.confirmPassword) {
      toast.error("As senhas não coincidem!");
      return;
    }

    createUserMutation.mutate(newUserForm);
  };

  const handleGeneratePDF = () => {
    if (userCreated) {
      generateCredentialsPDF(userCreated);
      toast.success("PDF de credenciais gerado com sucesso!");
    }
  };

  const handleCloseModal = () => {
    setUserCreated(null);
    setNewUserForm(initialNewUserForm);
    onClose();
  };
  
  return (
    <form onSubmit={handleNewUserSubmit}>
      <div className="grid gap-4 py-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Nome <span className="text-red-500">*</span></label>
          <Input
            placeholder="Nome completo do usuário"
            value={newUserForm.name}
            onChange={(e) => setNewUserForm({...newUserForm, name: e.target.value})}
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Email <span className="text-red-500">*</span></label>
          <Input
            type="email"
            placeholder="<EMAIL>"
            value={newUserForm.email}
            onChange={(e) => setNewUserForm({...newUserForm, email: e.target.value})}
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Telefone</label>
            <Input
              placeholder="Número de telefone"
              value={newUserForm.phone}
              onChange={(e) => setNewUserForm({...newUserForm, phone: e.target.value})}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Perfil <span className="text-red-500">*</span></label>
            <Select
              value={newUserForm.role}
              onValueChange={(value: "administrador" | "gerente" | "estagiario") => setNewUserForm({...newUserForm, role: value})}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="administrador">Administrador</SelectItem>
                <SelectItem value="gerente">Gerente</SelectItem>
                <SelectItem value="estagiario">Estagiário</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">Senha <span className="text-red-500">*</span></label>
          <PasswordInput
            placeholder="Senha provisória"
            value={newUserForm.password}
            onChange={(e) => setNewUserForm({...newUserForm, password: e.target.value})}
            showStrengthIndicator={true}
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Confirmar Senha <span className="text-red-500">*</span></label>
          <PasswordInput
            placeholder="Confirme a senha"
            value={newUserForm.confirmPassword}
            onChange={(e) => setNewUserForm({...newUserForm, confirmPassword: e.target.value})}
            required
          />
        </div>
      </div>
      
      <DialogFooter>
        {userCreated ? (
          // Show PDF generation options after user is created
          <>
            <div className="flex flex-col space-y-3 w-full">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-green-800 font-medium">Usuário criado com sucesso!</span>
                </div>
                <p className="text-sm text-green-700">
                  O usuário <strong>{userCreated.name}</strong> foi criado e pode fazer login com as credenciais fornecidas.
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <FileText className="w-4 h-4 text-blue-600" />
                  <span className="text-blue-800 font-medium">Gerar PDF de Credenciais</span>
                </div>
                <p className="text-sm text-blue-700 mb-3">
                  Gere um documento PDF com as credenciais de acesso para entregar ao usuário.
                </p>
                <Button
                  onClick={handleGeneratePDF}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Baixar PDF de Credenciais
                </Button>
              </div>
            </div>

            <div className="flex justify-end space-x-2 w-full mt-4">
              <Button
                variant="outline"
                onClick={handleCloseModal}
                type="button"
              >
                Fechar
              </Button>
            </div>
          </>
        ) : (
          // Show normal form buttons
          <>
            <Button
              variant="outline"
              onClick={handleCloseModal}
              type="button"
            >
              Cancelar
            </Button>
            <Button
              className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
              type="submit"
              disabled={createUserMutation.isPending}
            >
              {createUserMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Salvando...
                </>
              ) : (
                'Salvar'
              )}
            </Button>
          </>
        )}
      </DialogFooter>
    </form>
  );
};
