import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import { Loader2, User, Mail } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface ProfileData {
  id: string;
  name: string;
  email: string;
}

interface ProfileEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ProfileEditDialog = ({ isOpen, onClose }: ProfileEditDialogProps) => {
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    newPassword: "",
    confirmPassword: "",
  });
  const queryClient = useQueryClient();

  // Fetch current user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      setIsLoading(true);
      try {
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
          toast.error("Sessão expirada. Por favor, faça login novamente.");
          return;
        }

        const { data, error } = await supabase
          .from('profiles')
          .select('id, name, email')
          .eq('id', session.user.id)
          .single();

        if (error) throw error;

        setProfileData(data);
        setFormData(prev => ({
          ...prev,
          name: data.name || "",
        }));
      } catch (error) {
        console.error("Erro ao carregar perfil:", error);
        toast.error("Não foi possível carregar os dados do perfil.");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchUserProfile();
    }
  }, [isOpen]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async () => {
      // Validate passwords if user is trying to change password
      if (formData.newPassword) {
        if (formData.newPassword !== formData.confirmPassword) {
          throw new Error("As senhas não coincidem.");
        }

        // Update password
        const { error: updatePasswordError } = await supabase.auth.updateUser({
          password: formData.newPassword,
        });

        if (updatePasswordError) {
          throw updatePasswordError;
        }
      }

      // Update profile data
      const { error: updateProfileError } = await supabase
        .from('profiles')
        .update({
          name: formData.name,
        })
        .eq('id', profileData?.id);

      if (updateProfileError) {
        throw updateProfileError;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.success("Perfil atualizado com sucesso!");
      onClose();

      // Reset form
      setFormData({
        name: "",
        newPassword: "",
        confirmPassword: "",
      });
    },
    onError: (error: Error) => {
      console.error(error);
      toast.error(`Erro ao atualizar perfil: ${error.message}`);
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[450px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Editar Meu Perfil</DialogTitle>
          <DialogDescription>
            Atualize suas informações pessoais e senha.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-twtwins-purple" />
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6 py-4">
            {/* Informações Pessoais */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Informações Pessoais</h3>

              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Nome <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Seu nome completo"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email
                </Label>
                <Input
                  value={profileData?.email || ""}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground">O email não pode ser alterado.</p>
              </div>
            </div>

            {/* Alteração de Senha */}
            <div className="space-y-4 pt-2 border-t">
              <h3 className="text-sm font-medium text-muted-foreground pt-2">Alteração de Senha</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="newPassword">Nova Senha</Label>
                  <PasswordInput
                    id="newPassword"
                    name="newPassword"
                    value={formData.newPassword}
                    onChange={handleChange}
                    placeholder="Digite a nova senha"
                    showStrengthIndicator={true}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirmar Nova Senha</Label>
                  <PasswordInput
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    placeholder="Confirme a nova senha"
                  />
                </div>
              </div>

              <p className="text-xs text-muted-foreground">
                Deixe os campos de senha em branco se não desejar alterá-la.
              </p>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
                disabled={updateProfileMutation.isPending}
              >
                {updateProfileMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  "Salvar Alterações"
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};
