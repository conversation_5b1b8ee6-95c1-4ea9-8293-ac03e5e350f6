import React from "react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface UserAvatarProps {
  name: string;
  className?: string;
  title?: string;
}

export const UserAvatar = ({ name, className, title }: UserAvatarProps) => {
  // Get initials from name (up to 2 characters)
  const initials = name
    ? name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .substring(0, 2)
    : "--";

  return (
    <Avatar className={cn("bg-twtwins-purple/10", className)} title={title}>
      <AvatarFallback className="text-twtwins-purple font-medium">
        {initials}
      </AvatarFallback>
    </Avatar>
  );
};
