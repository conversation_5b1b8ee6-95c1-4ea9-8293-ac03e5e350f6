
import { Column } from "@/components/DataTable";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Pencil, Trash2 } from "lucide-react";
import { UserAvatar } from "./UserAvatar";
import { UserStatusBadge } from "./UserStatusBadge";
import { User } from "./EditUserForm";

interface UserColumnsProps {
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  userRole?: string | null;
}

export const getUserColumns = ({ onEdit, onDelete, userRole }: UserColumnsProps): Column<User>[] => [
  {
    header: "USUÁRIO",
    accessorKey: (row: User) => (
      <div className="flex items-center gap-3">
        <UserAvatar name={row.name} />
        <div>
          <div className="font-medium">{row.name}</div>
          <div className="text-sm text-muted-foreground">{row.email}</div>
        </div>
      </div>
    ),
    className: "min-w-[250px]",
  },
  {
    header: "PERFIL",
    accessorKey: (row: User) => row.role,
  },
  {
    header: "STATUS",
    accessorKey: (row: User) => <UserStatusBadge status={row.status} />,
  },
  {
    header: "TELEFONE",
    accessorKey: (row: User) => row.phone || "Não informado",
  },
  {
    header: "ÚLTIMO LOGIN",
    accessorKey: (row: User) => {
      if (!row.last_login) return "Nunca";
      return new Date(row.last_login).toLocaleString('pt-BR');
    },
  },
  {
    header: "AÇÕES",
    accessorKey: (row: User) => {
      const isAdmin = userRole === 'administrador';
      
      return (
        <div className="flex justify-end space-x-2">
          {isAdmin ? (
            <>
              <Button 
                variant="outline" 
                size="icon" 
                className="h-8 w-8" 
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(row);
                }}
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="icon" 
                className="h-8 w-8 text-red-600"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(row);
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </>
          ) : (
            <span className="text-sm text-muted-foreground">Visualização apenas</span>
          )}
        </div>
      );
    },
    className: "w-[130px]",
  },
];
