
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { NewUserForm } from "./NewUserForm";
import { EditUserForm, User } from "./EditUserForm";
import { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, deleteUserById } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { PermissionAlert } from "./PermissionAlert";

interface UserDialogsProps {
  isNewUserOpen: boolean;
  setIsNewUserOpen: (isOpen: boolean) => void;
  isEditDialogOpen: boolean;
  setIsEditDialogOpen: (isOpen: boolean) => void;
  isDeleteDialogOpen: boolean;
  setIsDeleteDialogOpen: (isOpen: boolean) => void;
  selectedUser: User | null;
  editUserForm: Partial<User>;
  setEditUserForm: (user: Partial<User>) => void;
  userRole: string | null;
}

export const UserDialogs = ({
  isNewUserOpen,
  setIsNewUserOpen,
  isEditDialogOpen,
  setIsEditDialogOpen,
  isDeleteDialogOpen,
  setIsDeleteDialogOpen,
  selectedUser,
  editUserForm,
  setEditUserForm,
  userRole,
}: UserDialogsProps) => {
  const queryClient = useQueryClient();
  const isAdmin = userRole === 'administrador';

  const deleteUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      const result = await deleteUserById(userId);

      if (result.error) {
        if (result.error.code === 'partial_success') {
          // Case of partial deletion (only profile was deleted)
          toast.warning(result.error.message);
        } else {
          // Outros erros - lançar para tratamento no onError
          throw new Error(result.error.message);
        }
      }

      return result.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setIsDeleteDialogOpen(false);

      // Show detailed success message with deletion results
      if (data?.deletionResults) {
        const { transactions, categories, subcategories, profile } = data.deletionResults;
        const totalItems = transactions + categories + subcategories + profile;

        toast.success(
          `Usuário excluído com sucesso! Removidos: ${transactions} transações, ${categories} categorias, ${subcategories} subcategorias e ${profile} perfil.`,
          { duration: 5000 }
        );
      } else {
        toast.success("Usuário excluído com sucesso!");
      }
    },
    onError: (error) => {
      console.error(error);
      toast.error(`Erro ao excluir usuário: ${error.message}`);
    }
  });

  return (
    <>
      {/* Diálogo de novo usuário */}
      <Dialog open={isNewUserOpen} onOpenChange={setIsNewUserOpen}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Novo Usuário</DialogTitle>
            <DialogDescription>
              Adicione um novo usuário ao sistema.
            </DialogDescription>
          </DialogHeader>

          {isAdmin ? (
            <NewUserForm onClose={() => setIsNewUserOpen(false)} />
          ) : (
            <PermissionAlert action="criar" />
          )}

          {!isAdmin && (
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsNewUserOpen(false)}
              >
                Fechar
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>

      {/* Diálogo de edição de usuário */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Usuário</DialogTitle>
            <DialogDescription>
              Altere as informações do usuário.
            </DialogDescription>
          </DialogHeader>

          {isAdmin ? (
            <EditUserForm
              user={editUserForm}
              onClose={() => setIsEditDialogOpen(false)}
            />
          ) : (
            <PermissionAlert action="editar" />
          )}

          {!isAdmin && (
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
              >
                Fechar
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>

      {/* Diálogo de confirmação de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="text-red-600">⚠️ Excluir Usuário</DialogTitle>
            <DialogDescription className="space-y-3">
              {isAdmin ? (
                <div className="space-y-3">
                  <p className="font-medium">
                    Você está prestes a excluir permanentemente o usuário <span className="font-bold text-red-600">{selectedUser?.name}</span>.
                  </p>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p className="font-medium text-red-800 mb-2">⚠️ ATENÇÃO: Esta ação irá remover TODOS os dados relacionados:</p>
                    <ul className="text-sm text-red-700 space-y-1 ml-4">
                      <li>• Todas as transações criadas pelo usuário</li>
                      <li>• Todas as categorias criadas pelo usuário</li>
                      <li>• Todas as subcategorias criadas pelo usuário</li>
                      <li>• O perfil e conta de acesso do usuário</li>
                    </ul>
                  </div>
                  <p className="text-sm font-medium text-red-600">
                    Esta ação é IRREVERSÍVEL e não pode ser desfeita.
                  </p>
                </div>
              ) : (
                <PermissionAlert action="excluir" />
              )}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              {isAdmin ? "Cancelar" : "Fechar"}
            </Button>
            {isAdmin && (
              <Button
                variant="destructive"
                onClick={() => selectedUser && deleteUserMutation.mutate(selectedUser.id)}
                disabled={deleteUserMutation.isPending}
                className="bg-red-600 hover:bg-red-700"
              >
                {deleteUserMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Excluindo todos os dados...
                  </>
                ) : (
                  'Confirmar Exclusão Permanente'
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
