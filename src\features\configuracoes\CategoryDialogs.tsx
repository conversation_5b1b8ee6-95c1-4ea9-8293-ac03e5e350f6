
import { useState, useEffect } from "react";
import { Category } from "./CategoryTable";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel
} from "@/components/ui/form";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const categorySchema = z.object({
  name: z.string().min(1, "Nome da categoria é obrigatório")
});

// Component to render a red asterisk for required fields
const RequiredFieldMarker = () => (
  <span className="text-red-500 ml-1">*</span>
);

export function CategoryDialogs({
  refreshCategories,
  isNewCategoryOpen,
  setIsNewCategoryOpen,
  isEditCategoryOpen,
  setIsEditCategoryOpen,
  isDeleteCategoryOpen,
  setIsDeleteCategoryOpen,
  selectedCategory,
}) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Form for creating a new category
  const categoryForm = useForm<z.infer<typeof categorySchema>>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: ""
    }
  });

  // Form for editing a category
  const editCategoryForm = useForm<z.infer<typeof categorySchema>>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: selectedCategory?.name || ""
    }
  });

  // Atualiza os valores do formulário quando o selectedCategory mudar
  useEffect(() => {
    if (selectedCategory && isEditCategoryOpen) {
      editCategoryForm.reset({
        name: selectedCategory.name
      });
    }
  }, [selectedCategory, isEditCategoryOpen]);

  // Create a new category
  const createCategory = async (values: z.infer<typeof categorySchema>) => {
    try {
      setIsLoading(true);
      
      const { data: userData } = await supabase.auth.getUser();
      
      const { error } = await supabase
        .from('categories')
        .insert([
          { 
            name: values.name,
            created_by: userData.user?.id
          }
        ])
        .select();
      
      if (error) throw error;
      
      toast({
        title: "Sucesso",
        description: "Categoria criada com sucesso!"
      });
      
      refreshCategories();
      setIsNewCategoryOpen(false);
      categoryForm.reset();
    } catch (error) {
      console.error('Erro ao criar categoria:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível criar a categoria."
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Update a category
  const updateCategory = async (values: z.infer<typeof categorySchema>) => {
    if (!selectedCategory) return;
    
    try {
      setIsLoading(true);
      
      const { error } = await supabase
        .from('categories')
        .update({ 
          name: values.name,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedCategory.id);
      
      if (error) throw error;
      
      toast({
        title: "Sucesso",
        description: "Categoria atualizada com sucesso!"
      });
      
      refreshCategories();
      setIsEditCategoryOpen(false);
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível atualizar a categoria."
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Delete a category
  const deleteCategory = async () => {
    if (!selectedCategory) return;
    
    try {
      setIsLoading(true);
      
      const { error } = await supabase
        .from('categories')
        .delete()
        .eq('id', selectedCategory.id);
      
      if (error) throw error;
      
      toast({
        title: "Sucesso",
        description: "Categoria excluída com sucesso!"
      });
      
      refreshCategories();
      setIsDeleteCategoryOpen(false);
    } catch (error) {
      console.error('Erro ao excluir categoria:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível excluir a categoria."
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Modal de Nova Categoria */}
      <Dialog open={isNewCategoryOpen} onOpenChange={setIsNewCategoryOpen}>
        <DialogContent className="sm:max-w-[400px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Nova Categoria</DialogTitle>
            <DialogDescription>
              Adicione uma nova categoria ao sistema.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...categoryForm}>
            <form onSubmit={categoryForm.handleSubmit(createCategory)} className="space-y-4">
              <FormField
                control={categoryForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Categoria<RequiredFieldMarker /></FormLabel>
                    <FormControl>
                      <Input placeholder="Digite o nome da categoria" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsNewCategoryOpen(false)}
                  type="button"
                >
                  Cancelar
                </Button>
                <Button 
                  className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? "Salvando..." : "Salvar"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Modal de Editar Categoria */}
      <Dialog open={isEditCategoryOpen} onOpenChange={setIsEditCategoryOpen}>
        <DialogContent className="sm:max-w-[400px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Categoria</DialogTitle>
            <DialogDescription>
              Edite os dados da categoria.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...editCategoryForm}>
            <form onSubmit={editCategoryForm.handleSubmit(updateCategory)} className="space-y-4">
              <FormField
                control={editCategoryForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Categoria<RequiredFieldMarker /></FormLabel>
                    <FormControl>
                      <Input placeholder="Digite o nome da categoria" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsEditCategoryOpen(false)}
                  type="button"
                >
                  Cancelar
                </Button>
                <Button 
                  className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? "Salvando..." : "Salvar"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Confirmação de exclusão de Categoria */}
      <AlertDialog open={isDeleteCategoryOpen} onOpenChange={setIsDeleteCategoryOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Categoria</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta categoria? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteCategoryOpen(false)}>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={deleteCategory}
              className="bg-red-600 text-white hover:bg-red-700"
            >
              {isLoading ? "Excluindo..." : "Excluir"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
