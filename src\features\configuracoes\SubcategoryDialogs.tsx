
import { useState, useEffect } from "react";
import { Category } from "./CategoryTable";
import { Subcategory } from "./SubcategoryTable";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel
} from "@/components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const subcategorySchema = z.object({
  name: z.string().min(1, "Nome da subcategoria é obrigatório"),
  category_id: z.string({ required_error: "Categoria é obrigatória" })
});

// Component to render a red asterisk for required fields
const RequiredFieldMarker = () => (
  <span className="text-red-500 ml-1">*</span>
);

export function SubcategoryDialogs({
  refreshSubcategories,
  categories,
  isNewSubcategoryOpen,
  setIsNewSubcategoryOpen,
  isEditSubcategoryOpen,
  setIsEditSubcategoryOpen,
  isDeleteSubcategoryOpen,
  setIsDeleteSubcategoryOpen,
  selectedSubcategory,
}) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Form for creating a new subcategory
  const subcategoryForm = useForm<z.infer<typeof subcategorySchema>>({
    resolver: zodResolver(subcategorySchema),
    defaultValues: {
      name: "",
      category_id: ""
    }
  });
  
  // Form for editing a subcategory
  const editSubcategoryForm = useForm<z.infer<typeof subcategorySchema>>({
    resolver: zodResolver(subcategorySchema),
    defaultValues: {
      name: "",
      category_id: ""
    }
  });

  // Atualiza os valores do formulário quando o selectedSubcategory mudar
  useEffect(() => {
    if (selectedSubcategory && isEditSubcategoryOpen) {
      editSubcategoryForm.reset({
        name: selectedSubcategory.name,
        category_id: selectedSubcategory.category_id || ""
      });
    }
  }, [selectedSubcategory, isEditSubcategoryOpen]);

  // Create a new subcategory
  const createSubcategory = async (values: z.infer<typeof subcategorySchema>) => {
    try {
      setIsLoading(true);
      
      const { data: userData } = await supabase.auth.getUser();
      
      const { error } = await supabase
        .from('subcategories')
        .insert([
          { 
            name: values.name,
            category_id: values.category_id,
            created_by: userData.user?.id
          }
        ])
        .select();
      
      if (error) throw error;
      
      toast({
        title: "Sucesso",
        description: "Subcategoria criada com sucesso!"
      });
      
      refreshSubcategories();
      setIsNewSubcategoryOpen(false);
      subcategoryForm.reset();
    } catch (error) {
      console.error('Erro ao criar subcategoria:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível criar a subcategoria."
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Update a subcategory
  const updateSubcategory = async (values: z.infer<typeof subcategorySchema>) => {
    if (!selectedSubcategory) return;
    
    try {
      setIsLoading(true);
      
      const { error } = await supabase
        .from('subcategories')
        .update({ 
          name: values.name,
          category_id: values.category_id,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedSubcategory.id);
      
      if (error) throw error;
      
      toast({
        title: "Sucesso",
        description: "Subcategoria atualizada com sucesso!"
      });
      
      refreshSubcategories();
      setIsEditSubcategoryOpen(false);
    } catch (error) {
      console.error('Erro ao atualizar subcategoria:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível atualizar a subcategoria."
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Delete a subcategory
  const deleteSubcategory = async () => {
    if (!selectedSubcategory) return;
    
    try {
      setIsLoading(true);
      
      const { error } = await supabase
        .from('subcategories')
        .delete()
        .eq('id', selectedSubcategory.id);
      
      if (error) throw error;
      
      toast({
        title: "Sucesso",
        description: "Subcategoria excluída com sucesso!"
      });
      
      refreshSubcategories();
      setIsDeleteSubcategoryOpen(false);
    } catch (error) {
      console.error('Erro ao excluir subcategoria:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível excluir a subcategoria."
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Modal de Nova Subcategoria */}
      <Dialog open={isNewSubcategoryOpen} onOpenChange={setIsNewSubcategoryOpen}>
        <DialogContent className="sm:max-w-[400px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Nova Subcategoria</DialogTitle>
            <DialogDescription>
              Adicione uma nova subcategoria ao sistema.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...subcategoryForm}>
            <form onSubmit={subcategoryForm.handleSubmit(createSubcategory)} className="space-y-4">
              <FormField
                control={subcategoryForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Subcategoria<RequiredFieldMarker /></FormLabel>
                    <FormControl>
                      <Input placeholder="Digite o nome da subcategoria" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={subcategoryForm.control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoria<RequiredFieldMarker /></FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma categoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem 
                            key={category.id} 
                            value={category.id}
                          >
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsNewSubcategoryOpen(false)}
                  type="button"
                >
                  Cancelar
                </Button>
                <Button 
                  className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? "Salvando..." : "Salvar"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Modal de Editar Subcategoria */}
      <Dialog open={isEditSubcategoryOpen} onOpenChange={setIsEditSubcategoryOpen}>
        <DialogContent className="sm:max-w-[400px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Subcategoria</DialogTitle>
            <DialogDescription>
              Edite os dados da subcategoria.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...editSubcategoryForm}>
            <form onSubmit={editSubcategoryForm.handleSubmit(updateSubcategory)} className="space-y-4">
              <FormField
                control={editSubcategoryForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Subcategoria<RequiredFieldMarker /></FormLabel>
                    <FormControl>
                      <Input placeholder="Digite o nome da subcategoria" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={editSubcategoryForm.control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoria<RequiredFieldMarker /></FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma categoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem 
                            key={category.id} 
                            value={category.id}
                          >
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsEditSubcategoryOpen(false)}
                  type="button"
                >
                  Cancelar
                </Button>
                <Button 
                  className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? "Salvando..." : "Salvar"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Confirmação de exclusão de Subcategoria */}
      <AlertDialog open={isDeleteSubcategoryOpen} onOpenChange={setIsDeleteSubcategoryOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Subcategoria</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta subcategoria? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteSubcategoryOpen(false)}>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={deleteSubcategory}
              className="bg-red-600 text-white hover:bg-red-700"
            >
              {isLoading ? "Excluindo..." : "Excluir"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
