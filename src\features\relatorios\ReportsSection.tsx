
import { cn } from "@/lib/utils";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, PieChart, TrendingUp, Download, Loader2 } from "lucide-react";
import { useState } from "react";
import { Transaction } from "@/hooks/useTransactions";
import { generateReport } from "@/utils/reportGenerator";
import { saveAs } from "file-saver";
import { toast } from "sonner";

interface Report {
  id: number;
  title: string;
  description: string;
  icon: React.ElementType;
  fileName: string;
  color: string;
  variant: string;
  buttonVisible: boolean;
}

interface ReportsSectionProps {
  transactions: Transaction[] | undefined;
  isLoading: boolean;
}

const ReportsSection = ({ transactions, isLoading }: ReportsSectionProps) => {
  const [generatingPdf, setGeneratingPdf] = useState<number | null>(null);

  // Get current month and year for the report header
  const currentDate = new Date();
  const monthYear = new Intl.DateTimeFormat('pt-AO', { month: 'long', year: 'numeric' }).format(currentDate);

  const reports: Report[] = [
    {
      id: 1,
      title: "Relatório de Fluxo de Caixa Mensal",
      description: "Resumo detalhado das entradas e saídas por mês",
      icon: BarChart,
      fileName: "fluxo-de-caixa-mensal",
      color: "green",
      variant: "green",
      buttonVisible: true
    },
    {
      id: 2,
      title: "Relatório de Transações por Categoria",
      description: "Análise de transações agrupadas por categorias",
      icon: PieChart,
      fileName: "transacoes-por-categoria",
      color: "purple",
      variant: "purple",
      buttonVisible: true
    },
    {
      id: 3,
      title: "Relatório de Projeções Financeiras",
      description: "Estimativas futuras baseadas no fluxo atual",
      icon: TrendingUp,
      fileName: "projecoes-financeiras",
      color: "blue",
      variant: "blue",
      buttonVisible: true
    },
  ];

  const handleDownload = async (reportId: number, fileName: string) => {
    if (!transactions || transactions.length === 0) {
      toast.error("Sem dados disponíveis", {
        description: "Não há transações para gerar o relatório."
      });
      return;
    }

    setGeneratingPdf(reportId);

    try {
      // Generate report based on ID
      const { blob, fileName: fullFileName } = await generateReport(
        reportId,
        fileName,
        transactions
      );

      // Save the generated file
      saveAs(blob, fullFileName);

      // Format for display in toast
      const formatLabel = 'PDF';

      toast.success(
        <div className="flex flex-col">
          <span className="font-medium">Relatório baixado com sucesso</span>
          <span className="text-sm text-muted-foreground">{fullFileName}</span>
        </div>,
        {
          duration: 4000,
          action: {
            label: "Ver",
            onClick: () => {
              // Create temporary URL for the blob and open in new tab
              const url = URL.createObjectURL(blob);
              window.open(url, '_blank');
            }
          }
        }
      );
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast.error("Erro ao gerar o relatório", {
        description: "Tente novamente mais tarde ou contate o suporte."
      });
    } finally {
      setGeneratingPdf(null);
    }
  };

  return (
    <div className="mb-8">
      <Card className="bg-white">
        <CardHeader className="pb-0">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h2 className="text-xl font-semibold">Relatórios Disponíveis</h2>
              <p className="text-muted-foreground text-sm">
                Período atual: <span className="font-medium">{monthYear}</span>
              </p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-2">
            {reports.map((report) => (
              <Card
                key={report.id}
                className={cn(
                  "hover:shadow-md transition-all duration-300 border",
                  `border-l-4 border-l-twtwins-${report.color}`,
                  "hover:translate-y-[-2px]"
                )}
              >
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className={cn(
                      "p-3 rounded-md flex items-center justify-center",
                      `bg-twtwins-${report.color}/10 text-twtwins-${report.color}`
                    )}>
                      <report.icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-base">{report.title}</h3>
                      <p className="text-sm text-muted-foreground mt-1">{report.description}</p>
                    </div>
                  </div>

                  <Button
                    variant="default"
                    size="sm"
                    className="mt-4 w-full flex items-center justify-center gap-2 bg-twtwins-purple hover:bg-twtwins-purple/90"
                    onClick={() => handleDownload(report.id, report.fileName)}
                    disabled={generatingPdf === report.id || isLoading}
                  >
                    {generatingPdf === report.id ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>Gerando PDF...</span>
                      </div>
                    ) : (
                      <>
                        <Download className="w-4 h-4" />
                        <span>Download PDF</span>
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsSection;
