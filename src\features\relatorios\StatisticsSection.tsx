
import StatCard from "@/components/StatCard";
import { FileText, TrendingUp, BarChart } from "lucide-react";
import { formatCurrency } from "@/lib/formatters";
import { Transaction } from "@/hooks/useTransactions";

interface StatisticsSectionProps {
  transactions: Transaction[] | undefined;
}

const StatisticsSection = ({ transactions }: StatisticsSectionProps) => {
  // Calculate basic statistics for summary
  const totalTransactions = transactions?.length || 0;
  const totalEntradas = transactions?.reduce((sum, t) =>
    t.type === "entrada" ? sum + t.amount : sum, 0) || 0;
  const totalSaidas = transactions?.reduce((sum, t) =>
    t.type === "saida" ? sum + t.amount : sum, 0) || 0;
  const saldo = totalEntradas - totalSaidas;

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
      <StatCard
        title="Total de Transações"
        value={totalTransactions.toString()}
        variant="purple"
        icon={<FileText className="h-5 w-5 text-twtwins-purple" />}
      />
      <StatCard
        title="Total de Entradas"
        value={formatCurrency(totalEntradas)}
        variant="green"
        icon={<TrendingUp className="h-5 w-5 text-twtwins-green" />}
      />
      <StatCard
        title="Total de Saídas"
        value={formatCurrency(totalSaidas)}
        variant="red"
        icon={<TrendingUp className="h-5 w-5 text-twtwins-red rotate-180" />}
      />
      <StatCard
        title="Saldo Atual"
        value={formatCurrency(saldo)}
        variant="blue"
        icon={<BarChart className="h-5 w-5 text-twtwins-blue" />}
      />
    </div>
  );
};

export default StatisticsSection;
