
import { useState, useMemo } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { DataTable } from "@/components/DataTable";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { formatDate, formatCurrency } from "@/lib/formatters";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Filter, FileSpreadsheet, Loader2, Calendar as CalendarIcon } from "lucide-react";
import { Transaction } from "@/hooks/useTransactions";
import { generateReport } from "@/utils/reportGenerator";
import { saveAs } from "file-saver";
import { toast } from "sonner";
import { format } from "date-fns";
import { pt } from "date-fns/locale";
import {
  Pagination,
  PaginationContent,
  Pa<PERSON>ationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface TransactionsSectionProps {
  transactions: Transaction[] | undefined;
  isLoading: boolean;
}

const TransactionsSection = ({ transactions, isLoading }: TransactionsSectionProps) => {
  const [periodFilter, setPeriodFilter] = useState("all");
  const [generatingPdf, setGeneratingPdf] = useState<number | null>(null);
  const [customDateFrom, setCustomDateFrom] = useState<Date | undefined>();
  const [customDateTo, setCustomDateTo] = useState<Date | undefined>();
  const [isCustomFilterApplied, setIsCustomFilterApplied] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Apply custom date filter
  const applyCustomDateFilter = () => {
    if (customDateFrom && customDateTo) {
      setIsCustomFilterApplied(true);
      setCurrentPage(1); // Reset to first page when applying filter
    } else {
      toast.error("Datas inválidas", {
        description: "Selecione uma data inicial e uma data final válidas."
      });
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setPeriodFilter("all");
    setCustomDateFrom(undefined);
    setCustomDateTo(undefined);
    setIsCustomFilterApplied(false);
    setCurrentPage(1); // Reset to first page when clearing filters
  };

  // Filter transactions based on selected period
  const filteredTransactions = transactions?.filter(transaction => {
    if (periodFilter === 'all') return true;

    const transactionDate = new Date(transaction.date);
    const today = new Date();

    // Mês atual
    if (periodFilter === 'month') {
      return transactionDate.getMonth() === today.getMonth() &&
             transactionDate.getFullYear() === today.getFullYear();
    }

    // Mês anterior
    if (periodFilter === 'lastMonth') {
      const lastMonth = new Date(today);
      lastMonth.setMonth(today.getMonth() - 1);
      return transactionDate.getMonth() === lastMonth.getMonth() &&
             transactionDate.getFullYear() === lastMonth.getFullYear();
    }

    // Ano atual
    if (periodFilter === 'year') {
      return transactionDate.getFullYear() === today.getFullYear();
    }

    // Ano anterior
    if (periodFilter === 'lastYear') {
      return transactionDate.getFullYear() === today.getFullYear() - 1;
    }

    // Período personalizado
    if (periodFilter === 'custom' && isCustomFilterApplied && customDateFrom && customDateTo) {
      // Ajustar a data final para incluir o dia inteiro
      const adjustedDateTo = new Date(customDateTo);
      adjustedDateTo.setHours(23, 59, 59, 999);

      return transactionDate >= customDateFrom && transactionDate <= adjustedDateTo;
    }

    return true;
  }) || [];

  // Pagination calculations
  const totalItems = filteredTransactions.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = filteredTransactions.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page: number) => {
    // Ensure page is within valid range
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  // Generate pagination items
  const paginationItems = useMemo(() => {
    const items = [];
    const maxVisiblePages = 5; // Maximum number of page buttons to show

    // Function to add a page button
    const addPageButton = (pageNum: number) => {
      items.push(
        <PaginationItem key={`page-${pageNum}`}>
          <PaginationLink
            isActive={currentPage === pageNum}
            onClick={() => handlePageChange(pageNum)}
            aria-label={`Página ${pageNum}`}
            className={cn(
              "bg-white hover:bg-muted/50 transition-colors hover:text-foreground",
              currentPage === pageNum ? "border-twtwins-purple text-twtwins-purple font-medium hover:text-twtwins-purple" : ""
            )}
          >
            {pageNum}
          </PaginationLink>
        </PaginationItem>
      );
    };

    // Always show first page
    if (totalPages > 0) {
      addPageButton(1);
    }

    // Add ellipsis after first page if needed
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Add pages around current page
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i <= 1 || i >= totalPages) continue; // Skip first and last pages (handled separately)
      addPageButton(i);
    }

    // Add ellipsis before last page if needed
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Always show last page if there's more than one page
    if (totalPages > 1) {
      addPageButton(totalPages);
    }

    return items;
  }, [currentPage, totalPages]);

  const handleDownload = async (reportId: number, fileName: string) => {
    if (!filteredTransactions || filteredTransactions.length === 0) {
      toast.error("Sem dados disponíveis", {
        description: "Não há transações para gerar o relatório."
      });
      return;
    }

    setGeneratingPdf(reportId);

    try {
      // Generate report based on ID
      const { blob, fileName: fullFileName } = await generateReport(
        reportId,
        fileName,
        filteredTransactions
      );

      // Save the generated file
      saveAs(blob, fullFileName);

      // Format for display in toast
      const formatLabel = 'PDF';

      toast.success(
        <div className="flex flex-col">
          <span className="font-medium">Relatório baixado com sucesso</span>
          <span className="text-sm text-muted-foreground">{fullFileName}</span>
        </div>,
        {
          duration: 4000,
          action: {
            label: "Ver",
            onClick: () => {
              // Create temporary URL for the blob and open in new tab
              const url = URL.createObjectURL(blob);
              window.open(url, '_blank');
            }
          }
        }
      );
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast.error("Erro ao gerar o relatório", {
        description: "Tente novamente mais tarde ou contate o suporte."
      });
    } finally {
      setGeneratingPdf(null);
    }
  };

  return (
    <Card className="mb-8">
      <CardHeader className="pb-0">
        <div>
          <h2 className="text-xl font-semibold">Transações</h2>
          <p className="text-muted-foreground text-sm">
            {filteredTransactions.length} transações encontradas
          </p>
        </div>
      </CardHeader>

      <CardContent className="pt-6">
        {/* Filters and Export button before table */}
        <div className="flex flex-wrap justify-between items-center mb-4 gap-4">
          <div className="flex flex-wrap items-center gap-2">
            <Select
              value={periodFilter}
              onValueChange={(value) => {
                setPeriodFilter(value);
                setCurrentPage(1); // Reset to first page when changing period filter
                if (value !== 'custom') {
                  setIsCustomFilterApplied(false);
                }
              }}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Período" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os períodos</SelectItem>
                <SelectItem value="month">Mês atual</SelectItem>
                <SelectItem value="lastMonth">Mês anterior</SelectItem>
                <SelectItem value="year">Ano atual</SelectItem>
                <SelectItem value="lastYear">Ano anterior</SelectItem>
                <SelectItem value="custom">Período personalizado</SelectItem>
              </SelectContent>
            </Select>

            {/* Campos de data para período personalizado */}
            {periodFilter === "custom" && (
              <>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-[130px] justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {customDateFrom ? (
                        format(customDateFrom, "dd/MM/yyyy")
                      ) : (
                        <span className="text-muted-foreground">Data inicial</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={customDateFrom}
                      onSelect={setCustomDateFrom}
                      initialFocus
                      locale={pt}
                    />
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-[130px] justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {customDateTo ? (
                        format(customDateTo, "dd/MM/yyyy")
                      ) : (
                        <span className="text-muted-foreground">Data final</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={customDateTo}
                      onSelect={setCustomDateTo}
                      initialFocus
                      locale={pt}
                      disabled={(date) =>
                        customDateFrom ? date < customDateFrom : false
                      }
                    />
                  </PopoverContent>
                </Popover>

                <Button
                  variant="secondary"
                  size="sm"
                  onClick={applyCustomDateFilter}
                  disabled={!customDateFrom || !customDateTo}
                >
                  Aplicar
                </Button>
              </>
            )}

            {(periodFilter !== "all" || isCustomFilterApplied) && (
              <Button
                variant="outline"
                size="sm"
                onClick={resetFilters}
              >
                Limpar Filtros
              </Button>
            )}
          </div>

          <Button
            variant="default"
            size="sm"
            className="flex items-center gap-2 bg-twtwins-purple hover:bg-twtwins-purple/90"
            onClick={() => handleDownload(999, "transacoes-completo")}
            disabled={generatingPdf === 999 || !filteredTransactions.length}
          >
            {generatingPdf === 999 ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Gerando PDF...</span>
              </div>
            ) : (
              <>
                <FileSpreadsheet className="w-4 h-4" />
                <span>Exportar PDF</span>
              </>
            )}
          </Button>
        </div>

        {/* DataTable */}
        <DataTable
          columns={[
            {
              header: "Descrição",
              accessorKey: "description"
            },
            {
              header: "Tipo",
              accessorKey: (row) => (
                <span className={cn(
                  "px-2 py-1 rounded-full text-xs font-medium",
                  row.type === "entrada"
                    ? "bg-twtwins-green/10 text-twtwins-green"
                    : "bg-twtwins-red/10 text-twtwins-red"
                )}>
                  {row.type === "entrada" ? "Entrada" : "Saída"}
                </span>
              )
            },
            {
              header: "Data",
              accessorKey: (row) => (
                <span className="font-medium">
                  {formatDate(row.date)}
                </span>
              )
            },
            {
              header: "Categoria",
              accessorKey: (row) => (
                <span className="px-2 py-1 bg-muted rounded-md text-xs">
                  {row.categoria || "Não categorizado"}
                </span>
              )
            },
            {
              header: "Valor",
              accessorKey: (row) => (
                <span className={cn(
                  "font-medium",
                  row.type === "entrada" ? "text-twtwins-green" : "text-twtwins-red"
                )}>
                  {formatCurrency(row.amount)}
                </span>
              ),
              className: "text-right"
            }
          ]}
          data={paginatedData}
          emptyMessage="Nenhuma transação encontrada."
          loading={isLoading}
        />

        {/* Pagination and controls below the table */}
        <div className="flex flex-col sm:flex-row justify-between items-center mt-6 gap-4">
          <div className="text-sm text-muted-foreground">
            Mostrando {startIndex + 1}-{Math.min(endIndex, totalItems)} de {totalItems} transações
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground mr-2">Itens por página:</span>
            <Select
              value={itemsPerPage.toString()}
              onValueChange={(value) => {
                setItemsPerPage(Number(value));
                setCurrentPage(1); // Reset to first page when changing items per page
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder="10" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-4 flex justify-center">
            <Pagination className="w-full max-w-xl">
              <PaginationContent className="flex flex-wrap justify-center gap-1">
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(currentPage - 1)}
                    aria-disabled={currentPage === 1}
                    className={cn(
                      "bg-white hover:bg-muted/50 transition-colors hover:text-foreground",
                      currentPage === 1 ? "pointer-events-none opacity-50" : ""
                    )}
                  >
                    <span>Anterior</span>
                  </PaginationPrevious>
                </PaginationItem>

                {paginationItems}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(currentPage + 1)}
                    aria-disabled={currentPage === totalPages}
                    className={cn(
                      "bg-white hover:bg-muted/50 transition-colors hover:text-foreground",
                      currentPage === totalPages ? "pointer-events-none opacity-50" : ""
                    )}
                  >
                    <span>Próximo</span>
                  </PaginationNext>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TransactionsSection;
