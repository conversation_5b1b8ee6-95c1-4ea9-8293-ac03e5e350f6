import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Transaction } from "@/types/transactions";

export const useFetchTransactions = () => {
  return useQuery({
    queryKey: ["transactions"],
    queryFn: fetchTransactions,
  });
};

// Function to fetch transactions from Supabase
export const fetchTransactions = async (): Promise<Transaction[]> => {
  const { data: userSession } = await supabase.auth.getSession();
  
  if (!userSession.session) {
    console.error("Transactions: User not authenticated");
    throw new Error("User not authenticated");
  }
  
  // First, check user's role
  const { data: userProfile, error: profileError } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", userSession.session.user.id)
    .single();
  
  if (profileError) {
    console.error("Transactions: Error fetching user profile:", profileError);
    throw new Error(profileError.message);
  }

  // Normalize role by trimming and converting to lowercase
  const userRoleRaw = userProfile?.role || "";
  const userRole = typeof userRoleRaw === 'string' ? userRoleRaw.trim().toLowerCase() : "";
  
  console.log("Transactions: User role (raw):", userProfile?.role);
  console.log("Transactions: User role (normalized):", userRole);
  console.log("Transactions: User ID:", userSession.session.user.id);
  
  // Build the query with proper filtering based on role
  let query = supabase
    .from("transactions")
    .select(`
      *,
      categories(name),
      subcategories(name)
    `)
    .order("date", { ascending: false });

  // Apply filtering based on role - estagiario users should only see their own data
  if (userRole === 'estagiario') {
    console.log(`Transactions: User has role "${userRole}" - Filtering to show only own transactions`);
    query = query.eq("created_by", userSession.session.user.id);
  } else if (userRole === 'administrador' || userRole === 'gerente') {
    console.log(`Transactions: User has role "${userRole}" - Should see all transactions`);
  } else {
    // For any other role, show only their own data as a safety measure
    console.log(`Transactions: User has role "${userRole}" - Filtering to show only own transactions`);
    query = query.eq("created_by", userSession.session.user.id);
  }
  
  const { data, error } = await query;

  if (error) {
    console.error("Transactions: Error fetching transactions:", error);
    throw new Error(error.message);
  }

  console.log("Transactions: Fetched transactions count:", data?.length || 0);
  if (data && data.length > 0) {
    console.log("Transactions: First transaction example:", JSON.stringify(data[0]));
  }

  // Make sure the type is correctly converted to "entrada" or "saida"
  return data.map((item) => ({
    ...item,
    type: (item.type === "entrada" || item.type === "saida") ? item.type as "entrada" | "saida" : "entrada",
    categoria: item.categories?.name || "",
    subcategoria: item.subcategories?.name || "",
  })) as Transaction[];
};
