
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Transaction } from "@/types/transactions";
import { v4 as uuidv4 } from 'uuid';

export const useTransactionMutations = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Create transaction mutation
  const createTransactionMutation = useMutation({
    mutationFn: createTransaction,
    onSuccess: (data) => {
      console.log("Transaction created successfully:", data);
      
      // Invalidate both transactions and dashboard-transactions
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard-transactions"] });
      
      toast({
        title: "Transação criada",
        description: "A transação foi criada com sucesso.",
      });
    },
    onError: (error) => {
      console.error("Error creating transaction:", error);
      
      toast({
        title: "Erro",
        description: `Erro ao criar transação: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Update transaction mutation
  const updateTransactionMutation = useMutation({
    mutationFn: updateTransaction,
    onSuccess: (data) => {
      console.log("Transaction updated successfully:", data);
      
      // Invalidate both transactions and dashboard-transactions
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard-transactions"] });
      
      toast({
        title: "Transação atualizada",
        description: "A transação foi atualizada com sucesso.",
      });
    },
    onError: (error) => {
      console.error("Error updating transaction:", error);
      
      toast({
        title: "Erro",
        description: `Erro ao atualizar transação: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Delete transaction mutation
  const deleteTransactionMutation = useMutation({
    mutationFn: deleteTransaction,
    onSuccess: (id) => {
      console.log("Transaction deleted successfully, ID:", id);
      
      // Invalidate both transactions and dashboard-transactions
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard-transactions"] });
      
      toast({
        title: "Transação excluída",
        description: "A transação foi excluída com sucesso.",
      });
    },
    onError: (error) => {
      console.error("Error deleting transaction:", error);
      
      toast({
        title: "Erro",
        description: `Erro ao excluir transação: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  return {
    createTransaction: createTransactionMutation.mutate,
    updateTransaction: updateTransactionMutation.mutate,
    deleteTransaction: deleteTransactionMutation.mutate,
  };
};

// Function to create a transaction
async function createTransaction(transaction: Omit<Transaction, "id">) {
  console.log("Creating transaction:", transaction);
  
  const { data: userSession } = await supabase.auth.getSession();
  if (!userSession.session) {
    throw new Error("Usuário não autenticado");
  }
  
  const userId = userSession.session.user.id;
  console.log("Creating transaction for user ID:", userId);
  
  const newTransaction = {
    ...transaction,
    id: uuidv4(),
    created_by: userId
  };
  
  const { data, error } = await supabase
    .from("transactions")
    .insert(newTransaction)
    .select()
    .single();

  if (error) {
    console.error("Error in createTransaction:", error);
    throw new Error(error.message);
  }

  return data;
}

// Function to update a transaction
async function updateTransaction(transaction: Transaction) {
  console.log("Updating transaction:", transaction);
  
  const { data: userSession } = await supabase.auth.getSession();
  if (!userSession.session) {
    throw new Error("Usuário não autenticado");
  }
  
  const { data, error } = await supabase
    .from("transactions")
    .update({
      ...transaction,
    })
    .eq("id", transaction.id)
    .select()
    .single();

  if (error) {
    console.error("Error in updateTransaction:", error);
    throw new Error(error.message);
  }

  return data;
}

// Function to delete a transaction
async function deleteTransaction(id: string) {
  console.log("Deleting transaction with ID:", id);
  
  const { error } = await supabase.from("transactions").delete().eq("id", id);

  if (error) {
    console.error("Error in deleteTransaction:", error);
    throw new Error(error.message);
  }

  return id;
}
