
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Category } from "@/features/configuracoes/CategoryTable";

export function useCategories() {
  const { toast } = useToast();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchCategories = async () => {
    try {
      setIsLoading(true);

      // Get current user session and role
      const { data: userSession } = await supabase.auth.getSession();

      if (!userSession.session) {
        console.error("Categories: User not authenticated");
        throw new Error("User not authenticated");
      }

      // Get user role
      const { data: userProfile, error: profileError } = await supabase
        .from("profiles")
        .select("role")
        .eq("id", userSession.session.user.id)
        .single();

      if (profileError) {
        console.error("Categories: Error fetching user profile:", profileError);
        throw new Error(profileError.message);
      }

      const userRole = userProfile?.role || "";
      console.log("Categories: User role:", userRole);

      // Build query with role-based filtering
      let query = supabase
        .from('categories')
        .select('*')
        .order('name');

      // Apply filtering based on role - estagiario users should only see their own data
      if (userRole === 'estagiario') {
        console.log("Categories: Filtering categories for estagiario user");
        query = query.eq("created_by", userSession.session.user.id);
      } else if (userRole === 'administrador' || userRole === 'gerente') {
        console.log(`Categories: Showing all categories for role "${userRole}"`);
      } else {
        // For any other role, show only their own data as a safety measure
        console.log("Categories: Filtering categories for regular user");
        query = query.eq("created_by", userSession.session.user.id);
      }

      const { data, error } = await query;

      if (error) throw error;

      setCategories(data || []);
    } catch (error) {
      console.error('Erro ao buscar categorias:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível carregar as categorias."
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return {
    categories,
    isLoading,
    fetchCategories
  };
}
