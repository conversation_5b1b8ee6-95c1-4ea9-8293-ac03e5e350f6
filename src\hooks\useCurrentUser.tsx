import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

interface CurrentUser {
  id: string;
  name: string;
  email: string;
  role: "administrador" | "gerente" | "estagiario";
}

export const useCurrentUser = () => {
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session?.user) {
          setCurrentUser(null);
          setIsLoading(false);
          return;
        }

        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('name, email, role')
          .eq('id', session.user.id)
          .single();

        if (profileError) {
          console.error('Error fetching current user profile:', profileError);
          setError(profileError.message);
          setIsLoading(false);
          return;
        }

        setCurrentUser({
          id: session.user.id,
          name: profileData.name,
          email: profileData.email,
          role: profileData.role as "administrador" | "gerente" | "estagiario"
        });
      } catch (err) {
        console.error('Error in useCurrentUser:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCurrentUser();

    // Listen for auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT') {
        setCurrentUser(null);
      } else if (event === 'SIGNED_IN' && session) {
        fetchCurrentUser();
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  return {
    currentUser,
    isLoading,
    error,
    isAdmin: currentUser?.role === 'administrador',
    isManager: currentUser?.role === 'gerente',
    isEstagiario: currentUser?.role === 'estagiario',
    canManageUsers: currentUser?.role === 'administrador' || currentUser?.role === 'gerente'
  };
};
