
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Subcategory } from "@/features/configuracoes/SubcategoryTable";

export function useSubcategories() {
  const { toast } = useToast();
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchSubcategories = async () => {
    try {
      setIsLoading(true);

      // Get current user session and role
      const { data: userSession } = await supabase.auth.getSession();

      if (!userSession.session) {
        console.error("Subcategories: User not authenticated");
        throw new Error("User not authenticated");
      }

      // Get user role
      const { data: userProfile, error: profileError } = await supabase
        .from("profiles")
        .select("role")
        .eq("id", userSession.session.user.id)
        .single();

      if (profileError) {
        console.error("Subcategories: Error fetching user profile:", profileError);
        throw new Error(profileError.message);
      }

      const userRole = userProfile?.role || "";
      console.log("Subcategories: User role:", userRole);

      // Build query with role-based filtering
      let query = supabase
        .from('subcategories')
        .select(`
          *,
          categories(name)
        `)
        .order('name');

      // Apply filtering based on role - estagiario users should only see their own data
      if (userRole === 'estagiario') {
        console.log("Subcategories: Filtering subcategories for estagiario user");
        query = query.eq("created_by", userSession.session.user.id);
      } else if (userRole === 'administrador' || userRole === 'gerente') {
        console.log(`Subcategories: Showing all subcategories for role "${userRole}"`);
      } else {
        // For any other role, show only their own data as a safety measure
        console.log("Subcategories: Filtering subcategories for regular user");
        query = query.eq("created_by", userSession.session.user.id);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Formata os dados para exibir o nome da categoria
      const formattedData = data?.map(item => ({
        ...item,
        category_name: item.categories?.name || 'Sem categoria'
      }));

      setSubcategories(formattedData || []);
    } catch (error) {
      console.error('Erro ao buscar subcategorias:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível carregar as subcategorias."
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSubcategories();
  }, []);

  return {
    subcategories,
    isLoading,
    fetchSubcategories
  };
}
