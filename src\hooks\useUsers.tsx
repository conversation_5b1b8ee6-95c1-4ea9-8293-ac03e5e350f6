
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { User } from "@/components/users/EditUserForm";
import { useMemo } from "react";
import { subMonths, startOfMonth, endOfMonth, parseISO, isWithinInterval } from "date-fns";

export const useUsers = (searchQuery: string = "") => {
  const {
    data: users = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      // First get profiles data
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .order('name', { ascending: true });

      if (profilesError) throw profilesError;

      // For now, just use the profiles data
      // The last_login field will be updated by the login process
      return profilesData.map(user => ({
        ...user,
        avatar: user.name ? user.name.substring(0, 2).toUpperCase() : '--',
        role: user.role as "administrador" | "gerente" | "estagiario",
      })) as User[];
    }
  });
  
  // Calculate users statistics including month-over-month change
  const usersStats = useMemo(() => {
    // Filter users based on search query
    const filteredUsers = users.filter(user => 
      user.name?.toLowerCase().includes(searchQuery.toLowerCase()) || 
      user.email?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Count active users
    const activeUsersCount = users.filter(user => user.status === true).length;
    
    // Calculate month-over-month percentage change for active users
    // For this demo, we'll simulate by checking last_login dates
    // In a real implementation, you might track when users were activated/deactivated
    
    const currentDate = new Date();
    const currentMonthStart = startOfMonth(currentDate);
    const currentMonthEnd = endOfMonth(currentDate);
    const previousMonthStart = startOfMonth(subMonths(currentDate, 1));
    const previousMonthEnd = endOfMonth(subMonths(currentDate, 1));
    
    // Count users who logged in this month (as a proxy for active users this month)
    // This is an approximation - in a real implementation, you might track when users became active
    const currentMonthActiveUsers = users.filter(user => {
      if (!user.last_login || !user.status) return false;
      
      const loginDate = parseISO(user.last_login as unknown as string);
      return isWithinInterval(loginDate, {
        start: currentMonthStart,
        end: currentMonthEnd
      });
    }).length;
    
    // Count users who logged in during the previous month
    const previousMonthActiveUsers = users.filter(user => {
      if (!user.last_login || !user.status) return false;
      
      const loginDate = parseISO(user.last_login as unknown as string);
      return isWithinInterval(loginDate, {
        start: previousMonthStart,
        end: previousMonthEnd
      });
    }).length;
    
    /**
     * Calculates percentage change between current and previous active users
     * Result is uncapped and will be visually capped in the UI component
     */
    let percentChange = 0;
    if (previousMonthActiveUsers === 0) {
      percentChange = currentMonthActiveUsers > 0 ? 100 : 0;
    } else {
      percentChange = Math.round(((currentMonthActiveUsers - previousMonthActiveUsers) / previousMonthActiveUsers) * 100 * 10) / 10;
    }
    
    console.log("Users: Current month active users:", currentMonthActiveUsers);
    console.log("Users: Previous month active users:", previousMonthActiveUsers);
    console.log("Users: Active users % change (uncapped):", percentChange);

    return {
      filteredUsers,
      activeUsersCount,
      activeUsersPercentChange: percentChange,
      isLoading,
      error
    };
  }, [users, searchQuery, isLoading, error]);
  
  return usersStats;
};
