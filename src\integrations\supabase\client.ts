
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://sktvllynmbwamqgehoru.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNrdHZsbHlubWJ3YW1xZ2Vob3J1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4NDY2OTQsImV4cCI6MjA2MTQyMjY5NH0.pAZy-MJw7GIpzpq7t_HsX0yYrgKi03c5aze0FTqbw3A";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    storage: localStorage
  }
});

// Function to delete a user (using the Edge Function)
export async function deleteUserById(userId: string) {
  try {
    // Call the Edge Function that uses the service role key to delete the user from auth
    const { data: functionData, error: functionError } = await supabase.functions.invoke('delete-user', {
      body: { userId }
    });

    if (functionError) {
      console.error("Error in Edge Function:", functionError);

      // If Edge Function deletion fails, try to delete only the profile as fallback
      console.warn("Attempting to delete only the profile as fallback...");
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) {
        console.error("Error deleting profile:", profileError);
        return {
          data: { user: null },
          error: {
            message: "Failed to delete user and profile: " + (profileError.message || functionError.message),
            name: "AuthApiError",
            status: 400,
            code: "user_delete_failed" as const
          }
        };
      }

      // Successfully deleted the profile, but not the authentication user
      return {
        data: { user: null },
        error: {
          message: "User partially removed. The authentication record may still exist.",
          name: "AuthApiError",
          status: 207,
          code: "partial_success" as const
        }
      };
    }

    // Success - user was completely deleted with cascade
    return {
      data: {
        user: null,
        deletionResults: functionData?.deletionResults || null
      },
      error: null
    };

  } catch (error) {
    console.error("Error deleting user:", error);
    return {
      data: { user: null },
      error: {
        message: error.message || "Unknown error deleting user",
        name: "AuthApiError",
        status: 500,
        code: "unknown_error" as const
      }
    };
  }
}
