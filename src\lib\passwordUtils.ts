export type PasswordStrength = 'weak' | 'medium' | 'strong';

export interface PasswordStrengthResult {
  strength: PasswordStrength;
  score: number;
  feedback: string[];
}

export const calculatePasswordStrength = (password: string): PasswordStrengthResult => {
  if (!password) {
    return {
      strength: 'weak',
      score: 0,
      feedback: ['Digite uma senha']
    };
  }

  let score = 0;
  const feedback: string[] = [];

  // Length check
  if (password.length >= 8) {
    score += 2;
  } else if (password.length >= 6) {
    score += 1;
    feedback.push('Use pelo menos 8 caracteres');
  } else {
    feedback.push('Muito curta (mínimo 6 caracteres)');
  }

  // Uppercase letters
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Adicione letras maiúsculas');
  }

  // Lowercase letters
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Adicione letras minúsculas');
  }

  // Numbers
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Adicione números');
  }

  // Special characters
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Adicione símbolos (!@#$%...)');
  }

  // Common patterns (reduce score)
  if (/(.)\1{2,}/.test(password)) {
    score -= 1;
    feedback.push('Evite repetir caracteres');
  }

  if (/123|abc|qwe|password|senha/i.test(password)) {
    score -= 2;
    feedback.push('Evite sequências comuns');
  }

  // Determine strength
  let strength: PasswordStrength;
  if (score >= 5) {
    strength = 'strong';
  } else if (score >= 3) {
    strength = 'medium';
  } else {
    strength = 'weak';
  }

  // If strong, clear feedback
  if (strength === 'strong') {
    feedback.length = 0;
    feedback.push('Senha forte!');
  }

  return {
    strength,
    score: Math.max(0, Math.min(6, score)),
    feedback
  };
};
