import { useState, useRef } from "react";
import { PageHeader } from "@/components/PageHeader";
import StatCard from "@/components/StatCard";
import { DataTable, Column } from "@/components/DataTable";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Pencil,
  Trash2,
  FilterIcon,
  Plus,
  Calendar as CalendarIcon,
  FileUp,
  Eye,
  FileText
} from "lucide-react";
import { format, parseISO } from "date-fns";
import { pt } from "date-fns/locale";
import "@/components/ui/date-button.css";
import { cn } from "@/lib/utils";
import { useTransactions, Transaction } from "@/hooks/useTransactions";
import { useCategories } from "@/hooks/useCategories";
import { useSubcategories } from "@/hooks/useSubcategories";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useToast } from "@/components/ui/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";

// Schema de validação para o formulário de transação
const transactionSchema = z.object({
  description: z.string().min(1, "A descrição é obrigatória"),
  amount: z.number().min(0.01, "O valor deve ser maior que zero"),
  date: z.date({
    required_error: "A data é obrigatória",
  }),
  type: z.enum(["entrada", "saida"], {
    required_error: "O tipo é obrigatório",
  }),
  category_id: z.string().optional(),
  subcategory_id: z.string().optional(),
  comprovativo_url: z.string().optional()
});

// Component to render a red asterisk for required fields
const RequiredFieldMarker = () => (
  <span className="text-destructive ml-1">*</span>
);

const FluxoDeCaixa = () => {
  const [isNewTransactionOpen, setIsNewTransactionOpen] = useState(false);
  const [isViewComprovativoOpen, setIsViewComprovativoOpen] = useState(false);
  const [isEditTransactionOpen, setIsEditTransactionOpen] = useState(false);
  const [isDeleteTransactionOpen, setIsDeleteTransactionOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [dateFrom, setDateFrom] = useState<Date>();
  const [dateTo, setDateTo] = useState<Date>();
  const [tipo, setTipo] = useState<string>("todos");
  const [categoria, setCategoria] = useState<string>("todas");
  const [subcategoria, setSubcategoria] = useState<string>("todas");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentComprovativoUrl, setCurrentComprovativoUrl] = useState("");

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { categories } = useCategories();
  const { subcategories } = useSubcategories();

  const {
    transactions = [],
    isLoading,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    uploadComprovativo,
    isUploading
  } = useTransactions();

  const form = useForm<z.infer<typeof transactionSchema>>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      description: "",
      category_id: "",
      subcategory_id: "",
      type: "entrada",
      date: new Date(),
      amount: 0,
      comprovativo_url: ""
    }
  });

  const filteredTransactions = transactions.filter((transaction) => {
    // Filtro de pesquisa
    if (searchQuery && !transaction.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Filtro de tipo
    if (tipo && tipo !== "todos" && transaction.type !== tipo) {
      return false;
    }

    // Filtro de categoria
    if (categoria && categoria !== "todas" && transaction.category_id !== categoria) {
      return false;
    }

    // Filtro de subcategoria
    if (subcategoria && subcategoria !== "todas" && transaction.subcategory_id !== subcategoria) {
      return false;
    }

    // Filtro de data inicial
    if (dateFrom && new Date(transaction.date) < dateFrom) {
      return false;
    }

    // Filtro de data final
    if (dateTo) {
      const endDate = new Date(dateTo);
      endDate.setHours(23, 59, 59);
      if (new Date(transaction.date) > endDate) {
        return false;
      }
    }

    return true;
  });

  const totalEntradas = filteredTransactions
    .filter((t) => t.type === "entrada")
    .reduce((sum, t) => sum + t.amount, 0);

  const totalSaidas = filteredTransactions
    .filter((t) => t.type === "saida")
    .reduce((sum, t) => sum + t.amount, 0);

  const saldo = totalEntradas - totalSaidas;

  const handleNewTransaction = () => {
    form.reset({
      description: "",
      category_id: "",
      subcategory_id: "",
      type: "entrada",
      date: new Date(),
      amount: 0,
      comprovativo_url: ""
    });
    form.clearErrors();
    setIsNewTransactionOpen(true);
  };

  const handleEditTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    form.reset({
      description: transaction.description,
      category_id: transaction.category_id || "",
      subcategory_id: transaction.subcategory_id || "",
      type: transaction.type,
      date: parseISO(transaction.date),
      amount: transaction.amount,
      comprovativo_url: transaction.comprovativo_url || ""
    });
    setIsEditTransactionOpen(true);
  };

  const handleDeleteTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setIsDeleteTransactionOpen(true);
  };

  const handleViewComprovativo = (url: string) => {
    setCurrentComprovativoUrl(url);
    setIsViewComprovativoOpen(true);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const url = await uploadComprovativo(file);
      if (url) {
        form.setValue("comprovativo_url", url);
        toast({
          title: "Upload concluído",
          description: "O comprovativo foi carregado com sucesso.",
        });
      }
    } catch (error: any) {
      toast({
        title: "Erro no upload",
        description: `Erro ao fazer upload: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const onSubmitCreate = (data: any) => {
    const newTransaction = {
      description: data.description,
      category_id: data.category_id || null,
      subcategory_id: data.subcategory_id || null,
      type: data.type as "entrada" | "saida",
      date: format(data.date, "yyyy-MM-dd"),
      amount: Number(data.amount),
      comprovativo_url: data.comprovativo_url || null
    };

    createTransaction(newTransaction);
    setIsNewTransactionOpen(false);
  };

  const onSubmitEdit = (data: any) => {
    if (!selectedTransaction) return;

    const updatedTransaction = {
      id: selectedTransaction.id,
      description: data.description,
      category_id: data.category_id || null,
      subcategory_id: data.subcategory_id || null,
      type: data.type as "entrada" | "saida",
      date: format(data.date, "yyyy-MM-dd"),
      amount: Number(data.amount),
      comprovativo_url: data.comprovativo_url || null
    };

    updateTransaction(updatedTransaction);
    setIsEditTransactionOpen(false);
  };

  const onConfirmDelete = () => {
    if (selectedTransaction) {
      deleteTransaction(selectedTransaction.id);
      setIsDeleteTransactionOpen(false);
    }
  };

  const columns: Column<Transaction>[] = [
    {
      header: "Descrição",
      accessorKey: "description",
    },
    {
      header: "Categoria",
      accessorKey: "categoria",
    },
    {
      header: "Subcategoria",
      accessorKey: "subcategoria",
    },
    {
      header: "Tipo",
      accessorKey: (row: Transaction) => (
        <span
          className={cn(
            "px-2 py-1 rounded text-xs font-medium",
            row.type === "entrada"
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          )}
        >
          {row.type === "entrada" ? "Entrada" : "Saída"}
        </span>
      ),
    },
    {
      header: "Data",
      accessorKey: (row: Transaction) => format(new Date(row.date), "dd/MM/yyyy"),
    },
    {
      header: "Valor",
      accessorKey: (row: Transaction) => (
        <span
          className={cn(
            row.type === "entrada" ? "text-green-600" : "text-red-600"
          )}
        >
          {(row.amount).toLocaleString("pt-AO", {
            style: "currency",
            currency: "AOA",
            minimumFractionDigits: 2,
          })}
        </span>
      ),
      className: "text-right",
    },
    {
      header: "Comprovativo",
      accessorKey: (row: Transaction) => (
        <div className="flex justify-center">
          {row.comprovativo_url ? (
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={(e) => {
                e.stopPropagation();
                handleViewComprovativo(row.comprovativo_url!);
              }}
            >
              {row.comprovativo_url.toLowerCase().endsWith('.pdf') ? (
                <FileText className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          ) : (
            <span className="text-muted-foreground text-xs">Nenhum</span>
          )}
        </div>
      ),
      className: "w-[100px]",
    },
    {
      header: "Ações",
      accessorKey: (row: Transaction) => (
        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={(e) => {
              e.stopPropagation();
              handleEditTransaction(row);
            }}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 text-red-600"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteTransaction(row);
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
      className: "w-[100px]",
    },
  ];

  // Input de arquivo escondido
  const hiddenFileInput = (
    <input
      ref={fileInputRef}
      type="file"
      accept="image/png,image/jpeg,application/pdf"
      className="hidden"
      onChange={handleFileChange}
    />
  );

  return (
    <div className="animate-fade-in">
      <PageHeader
        title="Fluxo de Caixa"
        action={{
          label: "Nova Transação",
          icon: <Plus size={16} />,
          onClick: handleNewTransaction,
        }}
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <StatCard
          title="Total de Entradas"
          value={(totalEntradas).toLocaleString("pt-AO", {
            style: "currency",
            currency: "AOA",
            minimumFractionDigits: 2,
          })}
          variant="green"
        />
        <StatCard
          title="Total de Saídas"
          value={(totalSaidas).toLocaleString("pt-AO", {
            style: "currency",
            currency: "AOA",
            minimumFractionDigits: 2,
          })}
          variant="red"
        />
        <StatCard
          title="Saldo"
          value={(saldo).toLocaleString("pt-AO", {
            style: "currency",
            currency: "AOA",
            minimumFractionDigits: 2,
          })}
          variant="blue"
        />
      </div>

      <div className="bg-white p-4 rounded-lg border shadow-sm mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <FilterIcon size={16} className="text-muted-foreground" />
            <h2 className="text-lg font-medium">Filtros Avançados</h2>
          </div>
          {(dateFrom || dateTo || tipo !== "todos" || categoria !== "todas" || subcategoria !== "todas" || searchQuery) && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setDateFrom(undefined);
                setDateTo(undefined);
                setTipo("todos");
                setCategoria("todas");
                setSubcategoria("todas");
                setSearchQuery("");
              }}
            >
              Limpar Filtros
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Período</label>
            <div className="flex gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal date-button"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateFrom ? (
                      format(dateFrom, "dd/MM/yyyy")
                    ) : (
                      <span className="text-muted-foreground">Data inicial</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 pointer-events-auto calendar-popover-content" align="start">
                  <Calendar
                    mode="single"
                    selected={dateFrom}
                    onSelect={setDateFrom}
                    initialFocus
                    locale={pt}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium invisible">Até</label>
            <div className="flex gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal date-button"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateTo ? (
                      format(dateTo, "dd/MM/yyyy")
                    ) : (
                      <span className="text-muted-foreground">Data final</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 pointer-events-auto calendar-popover-content" align="start">
                  <Calendar
                    mode="single"
                    selected={dateTo}
                    onSelect={setDateTo}
                    initialFocus
                    locale={pt}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Categoria</label>
            <Select value={categoria} onValueChange={setCategoria}>
              <SelectTrigger>
                <SelectValue placeholder="Todas as categorias" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todas">Todas as categorias</SelectItem>
                {categories?.map((cat) => (
                  <SelectItem key={cat.id} value={cat.id}>
                    {cat.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Subcategoria</label>
            <Select value={subcategoria} onValueChange={setSubcategoria}>
              <SelectTrigger>
                <SelectValue placeholder="Todas as subcategorias" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todas">Todas as subcategorias</SelectItem>
                {subcategories?.filter(sub =>
                  !categoria || categoria === 'todas' || sub.category_id === categoria
                ).map((sub) => (
                  <SelectItem key={sub.id} value={sub.id}>
                    {sub.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Tipo</label>
            <Select value={tipo} onValueChange={setTipo}>
              <SelectTrigger>
                <SelectValue placeholder="Todos os tipos" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os tipos</SelectItem>
                <SelectItem value="entrada">Entrada</SelectItem>
                <SelectItem value="saida">Saída</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="mb-4">
        <Input
          placeholder="Pesquisar transações..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <DataTable
        columns={columns}
        data={filteredTransactions}
        emptyMessage="Nenhuma transação encontrada."
        loading={isLoading}
      />

      {/* Modal de Nova Transação */}
      <Dialog open={isNewTransactionOpen} onOpenChange={setIsNewTransactionOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Nova Transação</DialogTitle>
            <DialogDescription>
              Adicione uma nova transação ao fluxo de caixa.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitCreate)} className="space-y-4">
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição<RequiredFieldMarker /></FormLabel>
                    <FormControl>
                      <Input placeholder="Descreva a transação" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valor<RequiredFieldMarker /></FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0,00"
                          className="text-right"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data<RequiredFieldMarker /></FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? (
                                format(field.value, "dd/MM/yyyy")
                              ) : (
                                <span className="text-muted-foreground">Selecione uma data</span>
                              )}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 pointer-events-auto calendar-popover-content" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date()}
                            initialFocus
                            locale={pt}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo<RequiredFieldMarker /></FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="entrada">Entrada</SelectItem>
                        <SelectItem value="saida">Saída</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Categoria</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories?.map((cat) => (
                            <SelectItem key={cat.id} value={cat.id}>
                              {cat.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subcategory_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subcategoria</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {subcategories
                            ?.filter((sub) => !form.watch("category_id") || sub.category_id === form.watch("category_id"))
                            .map((sub) => (
                              <SelectItem key={sub.id} value={sub.id}>
                                {sub.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="comprovativo_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comprovativo</FormLabel>
                    <div className="flex items-center gap-2">
                      <FormControl>
                        <Input
                          readOnly
                          value={field.value ? "Arquivo carregado" : "Nenhum arquivo selecionado"}
                          className="flex-grow"
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                      >
                        <FileUp className="h-4 w-4 mr-2" />
                        {isUploading ? "Carregando..." : "Upload"}
                      </Button>
                    </div>
                    {field.value && (
                      <div className="mt-2 text-sm text-blue-600">
                        <a href={field.value} target="_blank" rel="noopener noreferrer">
                          Visualizar arquivo
                        </a>
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsNewTransactionOpen(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit" className="bg-twtwins-purple hover:bg-twtwins-dark-purple">
                  Salvar
                </Button>
              </DialogFooter>
            </form>
          </Form>
          {hiddenFileInput}
        </DialogContent>
      </Dialog>

      {/* Modal de Editar Transação */}
      <Dialog open={isEditTransactionOpen} onOpenChange={setIsEditTransactionOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Editar Transação</DialogTitle>
            <DialogDescription>
              Edite os detalhes da transação.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitEdit)} className="space-y-4">
              {/* Mesmos campos do formulário de Nova Transação */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição<RequiredFieldMarker /></FormLabel>
                    <FormControl>
                      <Input placeholder="Descreva a transação" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valor<RequiredFieldMarker /></FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0,00"
                          className="text-right"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data<RequiredFieldMarker /></FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? (
                                format(field.value, "dd/MM/yyyy")
                              ) : (
                                <span className="text-muted-foreground">Selecione uma data</span>
                              )}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 pointer-events-auto calendar-popover-content" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date()}
                            initialFocus
                            locale={pt}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo<RequiredFieldMarker /></FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="entrada">Entrada</SelectItem>
                        <SelectItem value="saida">Saída</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Categoria</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories?.map((cat) => (
                            <SelectItem key={cat.id} value={cat.id}>
                              {cat.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subcategory_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subcategoria</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {subcategories
                            ?.filter((sub) => !form.watch("category_id") || sub.category_id === form.watch("category_id"))
                            .map((sub) => (
                              <SelectItem key={sub.id} value={sub.id}>
                                {sub.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="comprovativo_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comprovativo</FormLabel>
                    <div className="flex items-center gap-2">
                      <FormControl>
                        <Input
                          readOnly
                          value={field.value ? "Arquivo carregado" : "Nenhum arquivo selecionado"}
                          className="flex-grow"
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                      >
                        <FileUp className="h-4 w-4 mr-2" />
                        {isUploading ? "Carregando..." : "Upload"}
                      </Button>
                    </div>
                    {field.value && (
                      <div className="mt-2 text-sm text-blue-600">
                        <a href={field.value} target="_blank" rel="noopener noreferrer">
                          Visualizar arquivo
                        </a>
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditTransactionOpen(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit" className="bg-twtwins-purple hover:bg-twtwins-dark-purple">
                  Salvar
                </Button>
              </DialogFooter>
            </form>
          </Form>
          {hiddenFileInput}
        </DialogContent>
      </Dialog>

      {/* Modal de Excluir Transação */}
      <Dialog open={isDeleteTransactionOpen} onOpenChange={setIsDeleteTransactionOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta transação? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteTransactionOpen(false)}
            >
              Cancelar
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={onConfirmDelete}
            >
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Visualizar Comprovativo */}
      <Dialog open={isViewComprovativoOpen} onOpenChange={setIsViewComprovativoOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Comprovativo</DialogTitle>
          </DialogHeader>

          <div className="flex justify-center p-4">
            {currentComprovativoUrl ? (
              currentComprovativoUrl.toLowerCase().endsWith('.pdf') ? (
                <iframe
                  src={currentComprovativoUrl}
                  className="w-full h-[500px]"
                  title="PDF Comprovativo"
                />
              ) : (
                <img
                  src={currentComprovativoUrl}
                  alt="Comprovativo"
                  className="max-w-full max-h-[500px] object-contain"
                />
              )
            ) : (
              <p className="text-muted-foreground">Nenhum comprovativo disponível.</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsViewComprovativoOpen(false)}
            >
              Fechar
            </Button>
            {currentComprovativoUrl && (
              <Button
                type="button"
                onClick={() => window.open(currentComprovativoUrl, "_blank")}
              >
                Abrir em Nova Aba
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FluxoDeCaixa;
