
import { PageHeader } from "@/components/PageHeader";
import { useTransactions } from "@/hooks/useTransactions";
import StatisticsSection from "@/features/relatorios/StatisticsSection";
import ReportsSection from "@/features/relatorios/ReportsSection";
import TransactionsSection from "@/features/relatorios/TransactionsSection";

const Relatorios = () => {
  const { transactions, isLoading } = useTransactions();

  return (
    <div className="animate-fade-in">
      <PageHeader title="Relatórios" />

      {/* Summary statistics */}
      <StatisticsSection transactions={transactions} />

      {/* Available reports */}
      <ReportsSection transactions={transactions} isLoading={isLoading} />

      {/* Transactions table */}
      {transactions && !isLoading && (
        <TransactionsSection transactions={transactions} isLoading={isLoading} />
      )}
    </div>
  );
};

export default Relatorios;
