/**
 * Utility functions to translate and customize authentication error messages
 * for better user experience in Portuguese
 */

export interface AuthError {
  message: string;
  code?: string;
  status?: number;
}

/**
 * Translates Supabase authentication error messages to user-friendly Portuguese messages
 */
export function translateAuthError(error: AuthError): string {
  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code?.toLowerCase() || '';

  // Handle specific error codes first
  switch (errorCode) {
    case 'email_not_confirmed':
      return 'Email não confirmado. Verifique sua caixa de entrada e confirme seu email.';
    
    case 'invalid_credentials':
    case 'invalid_login_credentials':
      return 'Email ou senha incorretos. Verifique suas credenciais e tente novamente.';
    
    case 'email_address_invalid':
      return 'Formato de email inválido. Digite um email válido.';
    
    case 'password_too_short':
      return 'Senha muito curta. A senha deve ter pelo menos 6 caracteres.';
    
    case 'signup_disabled':
      return 'Cadastro de novos usuários está desabilitado. Entre em contato com o administrador.';
    
    case 'email_address_not_authorized':
      return 'Este email não está autorizado a acessar o sistema. Entre em contato com o administrador.';
    
    case 'too_many_requests':
      return 'Muitas tentativas de login. Aguarde alguns minutos antes de tentar novamente.';
    
    case 'network_error':
    case 'fetch_error':
      return 'Erro de conexão. Verifique sua internet e tente novamente.';
    
    case 'user_not_found':
      return 'Email não cadastrado no sistema. Verifique o email digitado.';
    
    case 'weak_password':
      return 'Senha muito fraca. Use uma combinação de letras, números e símbolos.';
    
    case 'email_taken':
      return 'Este email já está cadastrado no sistema.';
    
    case 'session_not_found':
      return 'Sessão expirada. Faça login novamente.';
    
    case 'refresh_token_not_found':
      return 'Sessão inválida. Faça login novamente.';
  }

  // Handle error messages patterns
  if (errorMessage.includes('invalid login credentials') || 
      errorMessage.includes('invalid credentials')) {
    return 'Email ou senha incorretos. Verifique suas credenciais e tente novamente.';
  }
  
  if (errorMessage.includes('email not found') || 
      errorMessage.includes('user not found')) {
    return 'Email não cadastrado no sistema. Verifique o email digitado.';
  }
  
  if (errorMessage.includes('wrong password') || 
      errorMessage.includes('incorrect password')) {
    return 'Senha incorreta. Tente novamente.';
  }
  
  if (errorMessage.includes('email already registered') || 
      errorMessage.includes('email already exists')) {
    return 'Este email já está cadastrado no sistema.';
  }
  
  if (errorMessage.includes('network') || 
      errorMessage.includes('fetch') || 
      errorMessage.includes('connection')) {
    return 'Erro de conexão. Verifique sua internet e tente novamente.';
  }
  
  if (errorMessage.includes('rate limit') || 
      errorMessage.includes('too many requests')) {
    return 'Muitas tentativas de login. Aguarde alguns minutos antes de tentar novamente.';
  }
  
  if (errorMessage.includes('email format') || 
      errorMessage.includes('invalid email')) {
    return 'Formato de email inválido. Digite um email válido.';
  }
  
  if (errorMessage.includes('password') && errorMessage.includes('short')) {
    return 'Senha muito curta. A senha deve ter pelo menos 6 caracteres.';
  }
  
  if (errorMessage.includes('session') && errorMessage.includes('expired')) {
    return 'Sessão expirada. Faça login novamente.';
  }
  
  if (errorMessage.includes('unauthorized') || 
      errorMessage.includes('access denied')) {
    return 'Acesso negado. Entre em contato com o administrador.';
  }
  
  if (errorMessage.includes('account') && errorMessage.includes('disabled')) {
    return 'Sua conta foi desativada. Entre em contato com o administrador.';
  }
  
  if (errorMessage.includes('account') && errorMessage.includes('locked')) {
    return 'Sua conta foi bloqueada temporariamente. Entre em contato com o administrador.';
  }

  // Default fallback message
  return 'Erro ao fazer login. Verifique suas credenciais e tente novamente.';
}

/**
 * Gets appropriate error message based on HTTP status codes
 */
export function getErrorMessageByStatus(status: number): string {
  switch (status) {
    case 400:
      return 'Dados inválidos. Verifique as informações digitadas.';
    case 401:
      return 'Email ou senha incorretos. Verifique suas credenciais.';
    case 403:
      return 'Acesso negado. Entre em contato com o administrador.';
    case 404:
      return 'Email não cadastrado no sistema.';
    case 422:
      return 'Dados inválidos. Verifique o formato do email e senha.';
    case 429:
      return 'Muitas tentativas de login. Aguarde alguns minutos.';
    case 500:
    case 502:
    case 503:
    case 504:
      return 'Erro interno do servidor. Tente novamente em alguns minutos.';
    default:
      return 'Erro inesperado. Tente novamente.';
  }
}

/**
 * Determines if an error is network-related
 */
export function isNetworkError(error: AuthError): boolean {
  const message = error.message?.toLowerCase() || '';
  return message.includes('network') || 
         message.includes('fetch') || 
         message.includes('connection') ||
         message.includes('timeout') ||
         error.code === 'network_error' ||
         error.code === 'fetch_error';
}

/**
 * Determines if an error is related to invalid credentials
 */
export function isCredentialsError(error: AuthError): boolean {
  const message = error.message?.toLowerCase() || '';
  const code = error.code?.toLowerCase() || '';
  
  return message.includes('invalid login credentials') ||
         message.includes('invalid credentials') ||
         message.includes('wrong password') ||
         code === 'invalid_credentials' ||
         code === 'invalid_login_credentials';
}

/**
 * Main function to get user-friendly error message
 */
export function getAuthErrorMessage(error: any): string {
  if (!error) {
    return 'Erro desconhecido. Tente novamente.';
  }

  // If it's already a string, try to translate it
  if (typeof error === 'string') {
    return translateAuthError({ message: error });
  }

  // If it has a message property
  if (error.message) {
    return translateAuthError({
      message: error.message,
      code: error.code,
      status: error.status
    });
  }

  // If it has a status code, use that
  if (error.status) {
    return getErrorMessageByStatus(error.status);
  }

  // Fallback
  return 'Erro ao fazer login. Verifique suas credenciais e tente novamente.';
}
