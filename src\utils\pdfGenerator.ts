import jsPDF from 'jspdf';

export interface UserCredentials {
  name: string;
  email: string;
  password: string;
  role: string;
  createdAt: string;
}

/**
 * Generates a PDF with user credentials
 */
export const generateCredentialsPDF = (credentials: UserCredentials): void => {
  const doc = new jsPDF();
  
  // Set document properties
  doc.setProperties({
    title: 'Credenciais de Usuário - TwTwins',
    subject: 'Credenciais de acesso ao sistema',
    author: '<PERSON><PERSON><PERSON> TwTwins',
    creator: 'TwTwins - Sistema de Gestão de Fluxo de Caixa'
  });

  // Colors
  const primaryColor = '#8B5CF6'; // twtwins-purple
  const darkColor = '#1F2937';
  const grayColor = '#6B7280';
  const lightGrayColor = '#F3F4F6';

  // Page dimensions
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  
  // Header with logo area
  doc.setFillColor(primaryColor);
  doc.rect(0, 0, pageWidth, 40, 'F');
  
  // Company name/logo
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(24);
  doc.setFont('helvetica', 'bold');
  doc.text('TwTwins', 20, 25);
  
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text('Sistema de Gestão de Fluxo de Caixa', 20, 32);

  // Title
  doc.setTextColor(darkColor);
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('Credenciais de Acesso', 20, 60);

  // Subtitle
  doc.setTextColor(grayColor);
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text('Informações de login para o sistema', 20, 70);

  // User information section
  let yPosition = 90;
  
  // Background for user info
  doc.setFillColor(lightGrayColor);
  doc.rect(15, yPosition - 5, pageWidth - 30, 80, 'F');
  
  // User details
  doc.setTextColor(darkColor);
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  
  // Name
  doc.text('Nome Completo:', 20, yPosition + 10);
  doc.setFont('helvetica', 'normal');
  doc.text(credentials.name, 20, yPosition + 20);
  
  // Email
  doc.setFont('helvetica', 'bold');
  doc.text('Email de Login:', 20, yPosition + 35);
  doc.setFont('helvetica', 'normal');
  doc.text(credentials.email, 20, yPosition + 45);
  
  // Password
  doc.setFont('helvetica', 'bold');
  doc.text('Senha Temporária:', 20, yPosition + 60);
  doc.setFont('helvetica', 'normal');
  doc.setTextColor('#DC2626'); // Red color for password
  doc.text(credentials.password, 20, yPosition + 70);
  
  // Role
  doc.setTextColor(darkColor);
  doc.setFont('helvetica', 'bold');
  doc.text('Perfil de Acesso:', 120, yPosition + 10);
  doc.setFont('helvetica', 'normal');
  doc.text(credentials.role.charAt(0).toUpperCase() + credentials.role.slice(1), 120, yPosition + 20);
  
  // Creation date
  doc.setFont('helvetica', 'bold');
  doc.text('Data de Criação:', 120, yPosition + 35);
  doc.setFont('helvetica', 'normal');
  doc.text(credentials.createdAt, 120, yPosition + 45);

  // Instructions section
  yPosition = 190;
  
  doc.setTextColor(darkColor);
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('Instruções de Primeiro Acesso', 20, yPosition);
  
  doc.setTextColor(grayColor);
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  
  const instructions = [
    '1. Acesse o sistema através do link fornecido pelo administrador',
    '2. Digite seu email de login no campo correspondente',
    '3. Digite a senha temporária fornecida acima',
    '4. Após o primeiro login, você será solicitado a alterar sua senha',
    '5. Escolha uma senha forte com pelo menos 8 caracteres',
    '6. Em caso de dúvidas, entre em contato com o suporte técnico'
  ];
  
  instructions.forEach((instruction, index) => {
    doc.text(instruction, 20, yPosition + 15 + (index * 8));
  });

  // Security notice
  yPosition = 250;
  
  doc.setFillColor('#FEF3C7'); // Yellow background
  doc.rect(15, yPosition - 5, pageWidth - 30, 25, 'F');
  
  doc.setTextColor('#92400E'); // Dark yellow text
  doc.setFontSize(10);
  doc.setFont('helvetica', 'bold');
  doc.text('⚠️ IMPORTANTE - SEGURANÇA', 20, yPosition + 5);
  
  doc.setFont('helvetica', 'normal');
  doc.text('• Mantenha suas credenciais em local seguro', 20, yPosition + 12);
  doc.text('• Não compartilhe sua senha com terceiros', 20, yPosition + 18);

  // Footer
  doc.setTextColor(grayColor);
  doc.setFontSize(9);
  doc.setFont('helvetica', 'normal');
  doc.text('TwTwins - Sistema de Gestão de Fluxo de Caixa', 20, pageHeight - 20);
  doc.text(`Documento gerado em: ${new Date().toLocaleString('pt-BR')}`, 20, pageHeight - 12);
  
  // Support contact
  doc.text('Suporte: <EMAIL> | Tel: (244) 900-000-000', 20, pageHeight - 4);

  // Generate filename
  const fileName = `credenciais_${credentials.name.replace(/\s+/g, '_').toLowerCase()}_${Date.now()}.pdf`;
  
  // Save the PDF
  doc.save(fileName);
};

/**
 * Generates a preview of the credentials PDF (returns base64 data URL)
 */
export const generateCredentialsPDFPreview = (credentials: UserCredentials): string => {
  const doc = new jsPDF();
  
  // Same content as above but return as data URL instead of saving
  // ... (same PDF generation code as above)
  
  return doc.output('datauristring');
};
