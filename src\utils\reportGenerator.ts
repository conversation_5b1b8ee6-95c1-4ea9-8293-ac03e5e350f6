
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { saveAs } from "file-saver";
import { Transaction } from "@/types/transactions";
import { formatCurrency, formatDate } from "@/lib/formatters";

// Função para adicionar cabeçalho ao PDF
const addHeader = (doc: jsPDF, title: string) => {
  const pageWidth = doc.internal.pageSize.width;

  // Adicionar data atual no topo direito
  doc.setFontSize(8); // Aumentado para melhor legibilidade
  doc.setTextColor(70, 70, 70);
  const currentDate = new Date().toLocaleDateString("pt-AO", {
    day: "2-digit",
    month: "long",
    year: "numeric",
  });

  // Abordagem mais simples: colocar os textos em posições separadas

  // Renderizar "Gerado em:" em negrito
  doc.setFont("helvetica", "bold");
  doc.text("Gerado em:", pageWidth - 61, 5);

  // Renderizar a data em fonte normal
  doc.setFont("helvetica", "normal");
  doc.text(currentDate, pageWidth - 20, 5, { align: "right" });

  // Adicionar logo na mesma linha que a data, com tamanho reduzido
  try {
    // Usando caminho relativo para o logo
    doc.addImage("/logo.png", "PNG", 16, 8, 40, 15); // x, y, width, height (tamanho reduzido)
  } catch (error) {
    console.error("Erro ao adicionar logo:", error);
  }

  // Adicionar título centralizado abaixo do logo
  doc.setFontSize(18);
  doc.setTextColor(70, 70, 70);
  doc.text(title, pageWidth / 2, 50, { align: "center" }); // Ajustado para ficar mais próximo do logo

  // Espaço após o cabeçalho
  return 60; // Reduzido para compensar o logo mais alto
};

// Função para adicionar rodapé ao PDF
const addFooter = (doc: jsPDF, pageNumber: number, totalPages: number) => {
  const pageWidth = doc.internal.pageSize.width;

  // Adicionar créditos de desenvolvimento
  doc.setFontSize(8);
  doc.setTextColor(136, 136, 136); // Cor cinza clara (#888888)
  doc.text("Desenvolvido por Carlos César", 20, 285);

  // Adicionar numeração de páginas
  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0); // Voltar para cor preta
  doc.text(`Página ${pageNumber} de ${totalPages}`, pageWidth - 20, 285, { align: "right" });
};

// Gerar relatório de fluxo de caixa mensal
export const generateCashFlowReport = (transactions: Transaction[]): Promise<Blob> => {
  return new Promise((resolve) => {
    const doc = new jsPDF();
    let startY = addHeader(doc, "Relatório de Fluxo de Caixa Mensal");

    // Agrupar transações por mês
    const groupedByMonth: Record<string, { entradas: number; saidas: number }> = {};

    transactions.forEach(transaction => {
      const date = new Date(transaction.date);
      const monthYear = date.toLocaleDateString("pt-AO", { month: "long", year: "numeric" });

      if (!groupedByMonth[monthYear]) {
        groupedByMonth[monthYear] = { entradas: 0, saidas: 0 };
      }

      if (transaction.type === "entrada") {
        groupedByMonth[monthYear].entradas += transaction.amount;
      } else {
        groupedByMonth[monthYear].saidas += transaction.amount;
      }
    });

    // Preparar dados para a tabela
    const tableData = Object.entries(groupedByMonth).map(([month, data]) => [
      month,
      formatCurrency(data.entradas),
      formatCurrency(data.saidas),
      formatCurrency(data.entradas - data.saidas)
    ]);

    // Adicionar tabela
    autoTable(doc, {
      head: [["Mês", "Entradas", "Saídas", "Saldo"]],
      body: tableData,
      startY,
      headStyles: { fillColor: [155, 51, 155], textColor: [255, 255, 255] },
      alternateRowStyles: { fillColor: [240, 240, 250] },
      margin: { top: startY }
    });

    // Adicionar rodapé
    addFooter(doc, 1, 1);

    resolve(doc.output("blob"));
  });
};

// Gerar relatório de transações por categoria
export const generateCategoryReport = (transactions: Transaction[]): Promise<Blob> => {
  return new Promise((resolve) => {
    const doc = new jsPDF();
    let startY = addHeader(doc, "Relatório de Transações por Categoria");

    // Agrupar transações por categoria
    const groupedByCategory: Record<string, { count: number; total: number; transactions: Transaction[] }> = {};

    transactions.forEach(transaction => {
      const category = transaction.categoria || "Não categorizado";

      if (!groupedByCategory[category]) {
        groupedByCategory[category] = { count: 0, total: 0, transactions: [] };
      }

      groupedByCategory[category].count += 1;
      groupedByCategory[category].total += transaction.amount;
      groupedByCategory[category].transactions.push(transaction);
    });

    // Preparar dados para a tabela principal
    const tableData = Object.entries(groupedByCategory).map(([category, data]) => [
      category,
      data.count.toString(),
      formatCurrency(data.total)
    ]);

    // Adicionar tabela principal de categorias
    autoTable(doc, {
      head: [["Categoria", "Quantidade", "Total"]],
      body: tableData,
      startY,
      headStyles: { fillColor: [155, 51, 155], textColor: [255, 255, 255] },
      alternateRowStyles: { fillColor: [240, 240, 250] },
      margin: { top: startY }
    });

    // Adicionar tabelas detalhadas por categoria
    let currentY = (doc as any).lastAutoTable.finalY + 20;

    // Verificar se precisamos de uma nova página para o detalhe
    if (currentY > 240) {
      doc.addPage();
      currentY = 20;
    }

    doc.setFontSize(14);
    doc.text("Detalhamento por Categoria", 20, currentY);
    currentY += 10;

    Object.entries(groupedByCategory).forEach(([category, data]) => {
      if (currentY > 240) {
        doc.addPage();
        currentY = 20;
      }

      doc.setFontSize(12);
      doc.text(`${category} (Total: ${formatCurrency(data.total)})`, 20, currentY);
      currentY += 8;

      const detailData = data.transactions.map(t => [
        formatDate(t.date),
        t.description,
        t.type === "entrada" ? "Entrada" : "Saída",
        formatCurrency(t.amount)
      ]);

      autoTable(doc, {
        head: [["Data", "Descrição", "Tipo", "Valor"]],
        body: detailData,
        startY: currentY,
        headStyles: { fillColor: [155, 51, 155], textColor: [255, 255, 255] },
        alternateRowStyles: { fillColor: [240, 240, 250] },
        margin: { top: currentY }
      });

      currentY = (doc as any).lastAutoTable.finalY + 15;
    });

    // Adicionar rodapé a todas as páginas
    const totalPages = doc.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      addFooter(doc, i, totalPages);
    }

    resolve(doc.output("blob"));
  });
};

// Gerar relatório de projeções financeiras
export const generateProjectionsReport = (transactions: Transaction[]): Promise<Blob> => {
  return new Promise((resolve) => {
    const doc = new jsPDF();
    let startY = addHeader(doc, "Relatório de Projeções Financeiras");

    // Analisar tendências dos últimos 6 meses
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyData: Record<string, { entradas: number; saidas: number }> = {};

    // Preencher os últimos 6 meses no objeto
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthYear = date.toLocaleDateString("pt-AO", { month: "long", year: "numeric" });
      monthlyData[monthYear] = { entradas: 0, saidas: 0 };
    }

    // Agrupar transações por mês
    transactions.forEach(transaction => {
      const date = new Date(transaction.date);
      if (date >= sixMonthsAgo) {
        const monthYear = date.toLocaleDateString("pt-AO", { month: "long", year: "numeric" });

        if (!monthlyData[monthYear]) {
          monthlyData[monthYear] = { entradas: 0, saidas: 0 };
        }

        if (transaction.type === "entrada") {
          monthlyData[monthYear].entradas += transaction.amount;
        } else {
          monthlyData[monthYear].saidas += transaction.amount;
        }
      }
    });

    // Calcular médias mensais
    const months = Object.keys(monthlyData);
    let totalEntradas = 0;
    let totalSaidas = 0;

    months.forEach(month => {
      totalEntradas += monthlyData[month].entradas;
      totalSaidas += monthlyData[month].saidas;
    });

    const avgEntradas = months.length > 0 ? totalEntradas / months.length : 0;
    const avgSaidas = months.length > 0 ? totalSaidas / months.length : 0;

    // Histórico para tabela
    const historyData = Object.entries(monthlyData).map(([month, data]) => [
      month,
      formatCurrency(data.entradas),
      formatCurrency(data.saidas),
      formatCurrency(data.entradas - data.saidas)
    ]);

    // Adicionar histórico recente
    doc.setFontSize(14);
    doc.text("Histórico dos Últimos 6 Meses", 20, startY);

    autoTable(doc, {
      head: [["Mês", "Entradas", "Saídas", "Saldo"]],
      body: historyData,
      startY: startY + 10,
      headStyles: { fillColor: [155, 51, 155], textColor: [255, 255, 255] },
      alternateRowStyles: { fillColor: [240, 240, 250] },
      margin: { top: startY + 10 }
    });

    // Projeções para os próximos 3 meses
    let currentY = (doc as any).lastAutoTable.finalY + 20;

    doc.setFontSize(14);
    doc.text("Projeções para os Próximos 3 Meses", 20, currentY);

    const projectionData = [];
    const currentDate = new Date();

    for (let i = 1; i <= 3; i++) {
      currentDate.setMonth(currentDate.getMonth() + 1);
      const monthYear = currentDate.toLocaleDateString("pt-AO", { month: "long", year: "numeric" });

      // Projeção com leve crescimento de 3% ao mês para entradas
      const projectedEntradas = avgEntradas * Math.pow(1.03, i);
      // Projeção com leve crescimento de 2% ao mês para saídas
      const projectedSaidas = avgSaidas * Math.pow(1.02, i);

      projectionData.push([
        monthYear,
        formatCurrency(projectedEntradas),
        formatCurrency(projectedSaidas),
        formatCurrency(projectedEntradas - projectedSaidas)
      ]);
    }

    autoTable(doc, {
      head: [["Mês", "Entradas (Projeção)", "Saídas (Projeção)", "Saldo (Projeção)"]],
      body: projectionData,
      startY: currentY + 10,
      headStyles: { fillColor: [155, 51, 155], textColor: [255, 255, 255] },
      alternateRowStyles: { fillColor: [240, 240, 250] },
      margin: { top: currentY + 10 }
    });

    // Adicionar notas explicativas
    currentY = (doc as any).lastAutoTable.finalY + 20;

    doc.setFontSize(12);
    doc.text("Notas sobre as Projeções:", 20, currentY);
    currentY += 8;

    doc.setFontSize(10);
    const notes = [
      "• As projeções são baseadas na média dos últimos 6 meses com ajuste de tendência.",
      "• Considera-se um crescimento mensal de 3% para entradas.",
      "• Considera-se um crescimento mensal de 2% para saídas.",
      "• Estas projeções são estimativas e podem variar conforme fatores externos.",
    ];

    notes.forEach(note => {
      doc.text(note, 20, currentY);
      currentY += 6;
    });

    // Adicionar rodapé a todas as páginas
    const totalPages = doc.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      addFooter(doc, i, totalPages);
    }

    resolve(doc.output("blob"));
  });
};

// Gerar relatório completo de transações
export const generateFullTransactionsReport = (transactions: Transaction[]): Promise<Blob> => {
  return new Promise((resolve) => {
    const doc = new jsPDF();
    let startY = addHeader(doc, "Relatório Completo de Transações");

    // Resumo dos totais
    const totalEntradas = transactions.reduce((sum, t) =>
      t.type === "entrada" ? sum + t.amount : sum, 0);
    const totalSaidas = transactions.reduce((sum, t) =>
      t.type === "saida" ? sum + t.amount : sum, 0);
    const saldo = totalEntradas - totalSaidas;

    doc.setFontSize(12);
    doc.text("Resumo Financeiro", 20, startY);
    startY += 8;

    doc.setFontSize(10);
    doc.text(`Total de Entradas: ${formatCurrency(totalEntradas)}`, 20, startY);
    startY += 6;
    doc.text(`Total de Saídas: ${formatCurrency(totalSaidas)}`, 20, startY);
    startY += 6;
    doc.text(`Saldo: ${formatCurrency(saldo)}`, 20, startY);
    startY += 10;

    // Preparar dados para a tabela de transações
    const tableData = transactions.map(t => [
      formatDate(t.date),
      t.description,
      t.categoria || "Não categorizado",
      t.type === "entrada" ? "Entrada" : "Saída",
      formatCurrency(t.amount)
    ]);

    // Adicionar tabela de transações
    autoTable(doc, {
      head: [["Data", "Descrição", "Categoria", "Tipo", "Valor"]],
      body: tableData,
      startY: startY + 5,
      headStyles: { fillColor: [155, 51, 155], textColor: [255, 255, 255] },
      alternateRowStyles: { fillColor: [240, 240, 250] },
      margin: { top: startY + 5 }
    });

    // Adicionar rodapé a todas as páginas
    const totalPages = doc.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      addFooter(doc, i, totalPages);
    }

    resolve(doc.output("blob"));
  });
};



// Função principal para gerar relatório com base no tipo
export const generateReport = async (
  reportId: number,
  fileName: string,
  transactions: Transaction[]
): Promise<{ blob: Blob; fileName: string }> => {
  let blob: Blob;

  // Gerar PDF baseado no ID do relatório
  switch (reportId) {
    case 1: // Fluxo de Caixa Mensal
      blob = await generateCashFlowReport(transactions);
      break;
    case 2: // Transações por Categoria
      blob = await generateCategoryReport(transactions);
      break;
    case 3: // Projeções Financeiras
      blob = await generateProjectionsReport(transactions);
      break;
    case 999: // Relatório completo de transações
      blob = await generateFullTransactionsReport(transactions);
      break;
    default:
      blob = await generateFullTransactionsReport(transactions);
  }

  return {
    blob,
    fileName: `${fileName}.pdf`
  };
};



