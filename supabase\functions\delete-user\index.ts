
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const supabaseUrl = 'https://sktvllynmbwamqgehoru.supabase.co';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

// Criar cliente Supabase com a service role key para ter permissões de admin
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { userId } = await req.json();

    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'userId é obrigatório' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Tentando excluir usuário com ID: ${userId}`);

    // Step 1: Delete all related data (cascade delete)
    console.log('Iniciando exclusão em cascata dos dados relacionados...');

    const deletionResults = {
      transactions: 0,
      subcategories: 0,
      categories: 0,
      profile: 0
    };

    // Delete transactions created by the user
    const { count: transactionsCount, error: transactionsError } = await supabaseAdmin
      .from('transactions')
      .delete({ count: 'exact' })
      .eq('created_by', userId);

    if (transactionsError) {
      console.error(`Erro ao excluir transações: ${transactionsError.message}`);
    } else {
      deletionResults.transactions = transactionsCount || 0;
      console.log(`${deletionResults.transactions} transações excluídas`);
    }

    // Delete subcategories created by the user
    const { count: subcategoriesCount, error: subcategoriesError } = await supabaseAdmin
      .from('subcategories')
      .delete({ count: 'exact' })
      .eq('created_by', userId);

    if (subcategoriesError) {
      console.error(`Erro ao excluir subcategorias: ${subcategoriesError.message}`);
    } else {
      deletionResults.subcategories = subcategoriesCount || 0;
      console.log(`${deletionResults.subcategories} subcategorias excluídas`);
    }

    // Delete categories created by the user
    const { count: categoriesCount, error: categoriesError } = await supabaseAdmin
      .from('categories')
      .delete({ count: 'exact' })
      .eq('created_by', userId);

    if (categoriesError) {
      console.error(`Erro ao excluir categorias: ${categoriesError.message}`);
    } else {
      deletionResults.categories = categoriesCount || 0;
      console.log(`${deletionResults.categories} categorias excluídas`);
    }

    // Delete user profile
    const { count: profileCount, error: profileError } = await supabaseAdmin
      .from('profiles')
      .delete({ count: 'exact' })
      .eq('id', userId);

    if (profileError) {
      console.error(`Erro ao excluir perfil: ${profileError.message}`);
    } else {
      deletionResults.profile = profileCount || 0;
      console.log(`${deletionResults.profile} perfil excluído`);
    }

    // Step 2: Delete user from authentication
    const { data, error } = await supabaseAdmin.auth.admin.deleteUser(userId);

    if (error) {
      console.error(`Erro ao excluir usuário da autenticação: ${error.message}`);
      return new Response(
        JSON.stringify({
          error: error.message,
          deletionResults: deletionResults
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Usuário e todos os dados relacionados excluídos com sucesso');
    return new Response(
      JSON.stringify({
        success: true,
        data,
        deletionResults: deletionResults
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error(`Erro inesperado: ${error.message}`);
    return new Response(
      JSON.stringify({ error: 'Erro interno ao processar a solicitação' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
